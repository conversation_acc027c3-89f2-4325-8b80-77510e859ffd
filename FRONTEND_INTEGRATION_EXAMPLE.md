# 前端集成示例

## 推荐系统前端集成指南

### 1. API调用示例

#### 1.1 获取个性化推荐
```javascript
// 获取个性化推荐视频
async function getRecommendedVideos() {
    try {
        const response = await fetch('/api/video/recommend', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${userToken}`,
                'Content-Type': 'application/json'
            }
        });
        const videos = await response.json();
        return videos;
    } catch (error) {
        console.error('获取推荐视频失败:', error);
        return [];
    }
}
```

#### 1.2 记录视频观看
```javascript
// 记录用户观看视频
async function recordVideoView(videoId, watchDuration, videoDuration) {
    try {
        const response = await fetch(`/api/video/recordView/${videoId}`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${userToken}`,
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: `watchDuration=${watchDuration}&videoDuration=${videoDuration}`
        });
        
        if (response.ok) {
            console.log('观看记录已保存');
        }
    } catch (error) {
        console.error('保存观看记录失败:', error);
    }
}
```

#### 1.3 获取热门视频
```javascript
// 获取热门视频列表
async function getHotVideos(count = 20) {
    try {
        const response = await fetch(`/api/video/hotVideos?count=${count}`);
        const videos = await response.json();
        return videos;
    } catch (error) {
        console.error('获取热门视频失败:', error);
        return [];
    }
}
```

#### 1.4 获取最新视频
```javascript
// 获取最新视频列表
async function getNewVideos(count = 20) {
    try {
        const response = await fetch(`/api/video/newVideos?count=${count}`);
        const videos = await response.json();
        return videos;
    } catch (error) {
        console.error('获取最新视频失败:', error);
        return [];
    }
}
```

#### 1.5 获取观看历史
```javascript
// 获取用户观看历史
async function getViewHistory(limit = 20) {
    try {
        const response = await fetch(`/api/video/viewHistory?limit=${limit}`, {
            headers: {
                'Authorization': `Bearer ${userToken}`
            }
        });
        const history = await response.json();
        return history;
    } catch (error) {
        console.error('获取观看历史失败:', error);
        return [];
    }
}
```

### 2. Vue.js 组件示例

#### 2.1 推荐视频组件
```vue
<template>
  <div class="recommended-videos">
    <h2>为你推荐</h2>
    <div class="video-grid">
      <div 
        v-for="video in recommendedVideos" 
        :key="video.id"
        class="video-card"
        @click="playVideo(video)"
      >
        <img :src="video.poster" :alt="video.description" />
        <div class="video-info">
          <h3>{{ video.description }}</h3>
          <p>{{ video.user.username }}</p>
          <div class="video-stats">
            <span>👍 {{ video.likesSum }}</span>
            <span>💬 {{ video.commentsSum }}</span>
            <span>⭐ {{ video.collectionsSum }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RecommendedVideos',
  data() {
    return {
      recommendedVideos: []
    }
  },
  async mounted() {
    await this.loadRecommendedVideos();
  },
  methods: {
    async loadRecommendedVideos() {
      this.recommendedVideos = await getRecommendedVideos();
    },
    playVideo(video) {
      // 跳转到视频播放页面
      this.$router.push(`/video/${video.id}`);
    }
  }
}
</script>
```

#### 2.2 视频播放器组件（带观看记录）
```vue
<template>
  <div class="video-player">
    <video 
      ref="videoPlayer"
      :src="video.source"
      @loadedmetadata="onVideoLoaded"
      @timeupdate="onTimeUpdate"
      @ended="onVideoEnded"
      @pause="onVideoPaused"
      controls
    ></video>
  </div>
</template>

<script>
export default {
  name: 'VideoPlayer',
  props: {
    video: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      videoDuration: 0,
      watchDuration: 0,
      lastRecordTime: 0
    }
  },
  methods: {
    onVideoLoaded() {
      this.videoDuration = this.$refs.videoPlayer.duration;
    },
    onTimeUpdate() {
      const currentTime = this.$refs.videoPlayer.currentTime;
      this.watchDuration = Math.max(this.watchDuration, currentTime);
    },
    onVideoEnded() {
      this.recordView();
    },
    onVideoPaused() {
      // 每30秒记录一次观看进度
      if (this.watchDuration - this.lastRecordTime > 30) {
        this.recordView();
      }
    },
    async recordView() {
      if (this.watchDuration > 0 && this.videoDuration > 0) {
        await recordVideoView(
          this.video.id, 
          Math.floor(this.watchDuration), 
          Math.floor(this.videoDuration)
        );
        this.lastRecordTime = this.watchDuration;
      }
    }
  },
  beforeUnmount() {
    // 组件销毁时记录观看时长
    this.recordView();
  }
}
</script>
```

#### 2.3 热门视频页面
```vue
<template>
  <div class="hot-videos-page">
    <h1>热门视频</h1>
    <div class="filter-tabs">
      <button 
        v-for="tab in tabs" 
        :key="tab.key"
        :class="{ active: activeTab === tab.key }"
        @click="switchTab(tab.key)"
      >
        {{ tab.label }}
      </button>
    </div>
    <div class="video-list">
      <VideoCard 
        v-for="video in videos" 
        :key="video.id"
        :video="video"
      />
    </div>
  </div>
</template>

<script>
import VideoCard from '@/components/VideoCard.vue';

export default {
  name: 'HotVideosPage',
  components: {
    VideoCard
  },
  data() {
    return {
      activeTab: 'hot',
      videos: [],
      tabs: [
        { key: 'hot', label: '热门' },
        { key: 'new', label: '最新' },
        { key: 'recommend', label: '推荐' }
      ]
    }
  },
  async mounted() {
    await this.loadVideos();
  },
  methods: {
    async switchTab(tab) {
      this.activeTab = tab;
      await this.loadVideos();
    },
    async loadVideos() {
      switch (this.activeTab) {
        case 'hot':
          this.videos = await getHotVideos(50);
          break;
        case 'new':
          this.videos = await getNewVideos(50);
          break;
        case 'recommend':
          this.videos = await getRecommendedVideos();
          break;
      }
    }
  }
}
</script>
```

### 3. 观看历史页面
```vue
<template>
  <div class="view-history">
    <h1>观看历史</h1>
    <div class="history-list">
      <div 
        v-for="item in viewHistory" 
        :key="item.id"
        class="history-item"
      >
        <img :src="item.video.poster" :alt="item.video.description" />
        <div class="history-info">
          <h3>{{ item.video.description }}</h3>
          <p>{{ item.video.user.username }}</p>
          <div class="watch-progress">
            <div class="progress-bar">
              <div 
                class="progress-fill"
                :style="{ width: item.watchCompletionRate + '%' }"
              ></div>
            </div>
            <span>{{ Math.round(item.watchCompletionRate) }}%</span>
          </div>
          <p class="watch-time">{{ formatTime(item.createTime) }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ViewHistory',
  data() {
    return {
      viewHistory: []
    }
  },
  async mounted() {
    this.viewHistory = await getViewHistory(100);
  },
  methods: {
    formatTime(timeString) {
      return new Date(timeString).toLocaleString();
    }
  }
}
</script>

<style scoped>
.progress-bar {
  width: 100px;
  height: 4px;
  background-color: #eee;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #007bff;
  transition: width 0.3s ease;
}
</style>
```

### 4. 使用建议

1. **观看记录优化**: 建议每30秒或用户暂停时记录一次观看进度
2. **缓存策略**: 推荐结果可以在前端缓存5-10分钟
3. **错误处理**: 所有API调用都应该有适当的错误处理
4. **性能优化**: 使用虚拟滚动处理大量视频列表
5. **用户体验**: 添加加载状态和骨架屏
