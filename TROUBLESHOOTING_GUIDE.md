# 推荐系统故障排除指南

## 问题：项目启动后显示热门视频排行榜更新完成，然后停止运行

### 可能的原因和解决方案

#### 1. video_view 表不存在
**症状**: 日志显示"热门视频排行榜更新完成"后应用停止
**原因**: 定时任务尝试访问不存在的 video_view 表
**解决方案**:
```sql
-- 执行数据库迁移脚本
CREATE TABLE IF NOT EXISTS video_view (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    video_id BIGINT NOT NULL,
    watch_duration INT NOT NULL DEFAULT 0,
    video_duration INT NOT NULL DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_video_id (video_id),
    INDEX idx_create_time (create_time)
);
```

#### 2. Redis 连接问题
**症状**: 应用在Redis操作时停止
**原因**: Redis服务未启动或连接配置错误
**解决方案**:
- 检查Redis服务状态: `redis-server --version`
- 启动Redis服务: `redis-server`
- 检查连接配置: `application.yml` 中的Redis配置

#### 3. 定时任务启动时立即执行
**症状**: 应用启动时立即执行大量定时任务
**原因**: 定时任务没有设置初始延迟
**解决方案**: 已修复，现在所有定时任务都有初始延迟
- 热门视频更新: 启动后1分钟
- 新视频更新: 启动后2分钟  
- 分类缓存更新: 启动后3分钟

#### 4. 数据库连接池耗尽
**症状**: 大量数据库查询导致连接池耗尽
**原因**: 定时任务中的查询过于频繁
**解决方案**: 已优化查询逻辑，添加异常处理

### 修复后的改进

#### 1. 更好的错误处理
```java
try {
    long viewCount = videoViewRepository.getVideoViewCount(video.id());
    hotScore += viewCount;
} catch (Exception e) {
    // 如果获取观看数失败（比如表不存在），忽略这部分分数
    StaticLog.debug("获取视频{}观看数失败: {}", video.id(), e.getMessage());
}
```

#### 2. 启动延迟
```java
@Scheduled(fixedRate = 3600000, initialDelay = 60000) // 启动后1分钟开始
public void updateHotVideos() {
    // ...
}
```

#### 3. 系统检查
- 启动时检查Redis连接
- 检查关键表是否存在
- 记录详细的启动日志

### 部署建议

#### 1. 按顺序执行
1. 创建 video_view 表
2. 确保Redis服务运行
3. 重启应用
4. 观察启动日志

#### 2. 监控日志
关注以下日志信息:
- "开始系统启动检查..."
- "Redis连接正常"
- "系统启动检查完成，推荐系统将在1分钟后开始工作"
- "开始更新热门视频排行榜..."

#### 3. 如果仍有问题
1. 检查应用日志中的具体错误信息
2. 确认数据库连接正常
3. 确认Redis服务正常
4. 检查是否有其他定时任务冲突

### 临时禁用推荐系统
如果需要临时禁用推荐系统，可以注释掉 Application.java 中的 @EnableScheduling 注解:
```java
@SpringBootApplication
@EnableGracefulResponse
// @EnableScheduling  // 临时注释掉这行
public class Application {
    // ...
}
```

### 性能优化建议

1. **数据库索引**: 确保video表有适当的索引
2. **Redis配置**: 调整Redis内存和连接池设置
3. **定时任务频率**: 根据实际需要调整执行频率
4. **查询优化**: 限制查询的数据量，避免全表扫描

### 联系支持
如果问题仍然存在，请提供:
1. 完整的错误日志
2. 数据库表结构
3. Redis配置信息
4. 应用配置文件
