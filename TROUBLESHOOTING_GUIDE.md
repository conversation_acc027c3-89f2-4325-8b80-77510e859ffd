# 推荐系统故障排除指南

## 问题1：项目启动后显示热门视频排行榜更新完成，然后停止运行

### 可能的原因和解决方案

#### 1. video_view 表不存在

## 问题2：数据库验证错误 (DatabaseValidationException)

### 错误信息
```
Failed to validate database:
- com.celeste.entity.VideoView.watchCompletionRate: There is no column "watch_completion_rate" in table
- com.celeste.entity.VideoView.user: No foreign key constraint for columns: [USER_ID]
- com.celeste.entity.VideoView.video: No foreign key constraint for columns: [VIDEO_ID]
```

### 解决方案

#### 1. 重新创建video_view表
**原因**: Jimmer ORM验证数据库结构时发现问题
**解决方案**: 执行更新的数据库迁移脚本
```sql
-- 删除旧表（如果存在）
DROP TABLE IF EXISTS video_view;

-- 创建新的video_view表
CREATE TABLE video_view (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    video_id BIGINT NOT NULL COMMENT '视频ID',
    watch_duration INT NOT NULL DEFAULT 0 COMMENT '观看时长（秒）',
    video_duration INT NOT NULL DEFAULT 0 COMMENT '视频总时长（秒）',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',

    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_video_id (video_id),
    INDEX idx_create_time (create_time),
    INDEX idx_user_video (user_id, video_id),
    INDEX idx_user_create_time (user_id, create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频观看记录表';
```

#### 2. 实体修复说明
- `watchCompletionRate` 现在是 `@Formula` 计算属性，不映射到数据库
- 外键使用 `ForeignKeyType.FAKE`，避免数据库约束验证错误
- 明确指定了表名和列名映射

#### 3. Jimmer注解使用规则
- 默认方法只能使用 `@Formula` 注解，不能使用 `@Transient`
- `@Formula` 用于标记计算属性，在运行时计算值
- `@Formula` 的非抽象方法必须指定 `dependencies` 参数
- `dependencies` 数组包含计算所依赖的属性名称
- `@Transient` 用于普通属性，表示不映射到数据库

**正确的@Formula使用示例**:
```java
@Formula(dependencies = {"watchDuration", "videoDuration"})
default double watchCompletionRate() {
    if (videoDuration() <= 0) return 0.0;
    return Math.min(100.0, (double) watchDuration() / videoDuration() * 100);
}
```

#### 4. Jimmer ORM 操作返回类型
- `sql().insert()` 返回 `SimpleSaveResult<E>` 类型
- `sql().update()` 返回 `SimpleSaveResult<E>` 类型
- 需要调用 `.getModifiedEntity()` 获取实际的实体对象
- 用于获取插入或更新后的完整实体数据

**正确的数据库操作示例**:
```java
// 插入操作
VideoView newView = sql().insert(VideoViewDraft.$.produce(draft -> {
    // 设置属性...
})).getModifiedEntity();

// 更新操作
VideoView updatedView = sql().update(VideoViewDraft.$.produce(existingView, draft -> {
    // 修改属性...
})).getModifiedEntity();
```

#### 2. Redis 连接问题
**症状**: 应用在Redis操作时停止
**原因**: Redis服务未启动或连接配置错误
**解决方案**:
- 检查Redis服务状态: `redis-server --version`
- 启动Redis服务: `redis-server`
- 检查连接配置: `application.yml` 中的Redis配置

#### 3. 定时任务启动时立即执行
**症状**: 应用启动时立即执行大量定时任务
**原因**: 定时任务没有设置初始延迟
**解决方案**: 已修复，现在所有定时任务都有初始延迟
- 热门视频更新: 启动后1分钟
- 新视频更新: 启动后2分钟
- 分类缓存更新: 启动后3分钟

#### 4. 数据库连接池耗尽
**症状**: 大量数据库查询导致连接池耗尽
**原因**: 定时任务中的查询过于频繁
**解决方案**: 已优化查询逻辑，添加异常处理

### 修复后的改进

#### 1. 更好的错误处理
```java
try {
    long viewCount = videoViewRepository.getVideoViewCount(video.id());
    hotScore += viewCount;
} catch (Exception e) {
    // 如果获取观看数失败（比如表不存在），忽略这部分分数
    StaticLog.debug("获取视频{}观看数失败: {}", video.id(), e.getMessage());
}
```

#### 2. 启动延迟
```java
@Scheduled(fixedRate = 3600000, initialDelay = 60000) // 启动后1分钟开始
public void updateHotVideos() {
    // ...
}
```

#### 3. 系统检查
- 启动时检查Redis连接
- 检查关键表是否存在
- 记录详细的启动日志

### 部署建议

#### 1. 按顺序执行
1. 创建 video_view 表
2. 确保Redis服务运行
3. 重启应用
4. 观察启动日志

#### 2. 监控日志
关注以下日志信息:
- "开始系统启动检查..."
- "Redis连接正常"
- "系统启动检查完成，推荐系统将在1分钟后开始工作"
- "开始更新热门视频排行榜..."

#### 3. 如果仍有问题
1. 检查应用日志中的具体错误信息
2. 确认数据库连接正常
3. 确认Redis服务正常
4. 检查是否有其他定时任务冲突

### 临时禁用推荐系统
如果需要临时禁用推荐系统，可以注释掉 Application.java 中的 @EnableScheduling 注解:
```java
@SpringBootApplication
@EnableGracefulResponse
// @EnableScheduling  // 临时注释掉这行
public class Application {
    // ...
}
```

### 性能优化建议

1. **数据库索引**: 确保video表有适当的索引
2. **Redis配置**: 调整Redis内存和连接池设置
3. **定时任务频率**: 根据实际需要调整执行频率
4. **查询优化**: 限制查询的数据量，避免全表扫描

### 联系支持
如果问题仍然存在，请提供:
1. 完整的错误日志
2. 数据库表结构
3. Redis配置信息
4. 应用配置文件
