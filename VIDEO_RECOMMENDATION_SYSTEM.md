# 视频推荐系统优化

## 概述

本次优化为Celeste视频平台实现了智能推荐系统，包含热度推荐、兴趣推荐、关注推荐和定时任务等功能。

## 新增功能

### 1. 视频观看记录追踪
- **VideoView实体**: 记录用户观看视频的详细信息
- **观看时长统计**: 记录用户实际观看时长和视频总时长
- **观看完成度**: 自动计算观看完成百分比

### 2. 智能推荐算法

#### 2.1 热度推荐
- **综合评分算法**: 点赞数×2 + 评论数×3 + 收藏数×4 + 观看数×1
- **时间衰减因子**: 新视频获得额外加分，保持内容新鲜度
- **Redis缓存**: 使用有序集合存储热门视频排行榜

#### 2.2 个性化推荐
- **关注推荐** (30%): 推送用户关注博主的最新视频
- **兴趣推荐** (40%): 基于观看历史分析用户偏好
- **热门推荐** (20%): 平台热门内容
- **新视频推荐** (10%): 最新发布的视频

#### 2.3 兴趣分析
- **类型偏好**: 分析用户观看的视频类型分布
- **作者偏好**: 识别用户喜欢的视频创作者
- **协同过滤**: 基于相似用户行为推荐

### 3. 定时任务系统

#### 3.1 热门视频更新 (每小时)
```java
@Scheduled(fixedRate = 3600000)
public void updateHotVideos()
```
- 计算视频综合热度分数
- 更新Redis热门视频排行榜
- 应用时间衰减算法

#### 3.2 最新视频更新 (每30分钟)
```java
@Scheduled(fixedRate = 1800000)
public void updateNewVideos()
```
- 获取最近7天的新视频
- 按发布时间排序存储

#### 3.3 分类缓存更新 (每6小时)
```java
@Scheduled(fixedRate = 21600000)
public void updateVideoCategoryCache()
```
- 更新各视频分类的缓存数据
- 优化分类推荐性能

#### 3.4 缓存清理 (每天凌晨2点)
```java
@Scheduled(cron = "0 0 2 * * ?")
public void cleanExpiredCache()
```
- 清理过期的推荐缓存
- 维护系统性能

## 新增API接口

### 1. 视频观看记录
```http
POST /api/video/recordView/{videoId}
参数: watchDuration, videoDuration
功能: 记录用户观看视频的时长
```

### 2. 观看历史
```http
GET /api/video/viewHistory?limit=20
功能: 获取用户的观看历史记录
```

### 3. 热门视频
```http
GET /api/video/hotVideos?count=20
功能: 获取平台热门视频列表
```

### 4. 最新视频
```http
GET /api/video/newVideos?count=20
功能: 获取最新发布的视频列表
```

### 5. 增强推荐
```http
GET /api/video/recommend
功能: 获取个性化推荐视频（已优化）
```

## 数据库变更

### 新增表: video_view
```sql
CREATE TABLE video_view (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    video_id BIGINT NOT NULL,
    watch_duration INT NOT NULL DEFAULT 0,
    video_duration INT NOT NULL DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 配置要求

### 1. 启用定时任务
在Application.java中已添加`@EnableScheduling`注解

### 2. Redis配置
确保Redis服务正常运行，推荐系统依赖Redis缓存

### 3. 数据库迁移
执行`create_video_view_table.sql`脚本创建观看记录表

## 使用说明

### 1. 前端集成
在视频播放器中添加观看时长统计：
```javascript
// 视频播放结束或用户离开时调用
recordVideoView(videoId, watchDuration, videoDuration);
```

### 2. 推荐展示
- 首页推荐：调用`/api/video/recommend`
- 热门页面：调用`/api/video/hotVideos`
- 最新页面：调用`/api/video/newVideos`

### 3. 个人中心
- 观看历史：调用`/api/video/viewHistory`

## 性能优化

### 1. 缓存策略
- 热门视频缓存2小时
- 最新视频缓存1小时
- 分类缓存6小时更新

### 2. 数据库优化
- 添加必要的索引
- 观看记录去重逻辑
- 分页查询支持

### 3. 算法优化
- 权重分配可配置
- 推荐结果多样性
- 冷启动问题处理

## 监控和日志

系统会记录以下日志：
- 定时任务执行状态
- 推荐算法性能
- 缓存命中率
- 异常处理情况

## 部署步骤

### 1. 数据库迁移
```sql
-- 执行数据库迁移脚本
source celeste-server/src/main/resources/db/migration/create_video_view_table.sql
```

### 2. 重启应用
重启Spring Boot应用以启用定时任务和新功能

### 3. 验证功能
- 检查定时任务日志
- 测试推荐API接口
- 验证观看记录功能

## 测试建议

### 1. 单元测试
运行RecommendServiceTest验证核心逻辑

### 2. 集成测试
- 测试推荐API响应
- 验证Redis缓存功能
- 检查定时任务执行

### 3. 性能测试
- 推荐算法响应时间
- 数据库查询性能
- Redis缓存命中率

## 故障排除

### 1. 定时任务不执行
- 检查@EnableScheduling注解
- 查看应用日志
- 验证Spring配置

### 2. Redis连接问题
- 检查Redis服务状态
- 验证连接配置
- 查看RedisUtil日志

### 3. 推荐结果为空
- 检查基础数据
- 验证缓存状态
- 查看算法日志

## 未来扩展

1. **机器学习集成**: 可接入TensorFlow等ML框架
2. **实时推荐**: 基于用户实时行为调整推荐
3. **A/B测试**: 支持多种推荐策略对比
4. **用户画像**: 更详细的用户兴趣建模
5. **内容标签**: 基于视频内容的语义推荐
