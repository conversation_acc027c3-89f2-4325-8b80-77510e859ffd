package com.celeste.controller;

import com.celeste.entity.UserFetcher;
import com.celeste.entity.Video;
import com.celeste.entity.VideoFetcher;
import com.celeste.entity.VideoView;
import com.celeste.model.SecurityUser;
import com.celeste.repository.VideoRepository;
import com.celeste.repository.VideoViewRepository;
import com.celeste.service.RecommendService;
import com.celeste.utils.MinioUtil;
import com.celeste.utils.RedisUtil;
import com.feiniaojin.gracefulresponse.GracefulResponseException;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import org.babyfish.jimmer.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/api/video")
public class VideoController {

    @Resource
    private VideoRepository videoRepository;

    @Resource
    private VideoViewRepository videoViewRepository;

    @Resource
    private RecommendService recommendService;

    @GetMapping("/recommend")
    public List<Video> recommend(Authentication authentication) {
        return recommendService.recommend(authentication);
    }

    @GetMapping("/recommendType")
    public List<Video> recommend(long typeId) {
        return recommendService.recommendType(typeId);
    }

    @GetMapping("/demand/{videoId}")
    public Video demand(@PathVariable long videoId) {
        return videoRepository.findById(videoId, VideoFetcher.$
                        .description().likesSum().source().commentsSum().collectionsSum().betweenTime()
                        .user(UserFetcher.$.username()))
                .orElseThrow(() -> new GracefulResponseException("该视频不存在"));
    }

    @GetMapping("/getUserVideos/{userId}")
    public List<Video> getSomeoneVideos(@PathVariable long userId) {
        return videoRepository.listVideosByUserId(userId);
    }

    @GetMapping("/getOtherVideos/{videoId}")
    public Page<Video> getOtherVideos(@PathVariable long videoId, Pageable pageable) {
        return videoRepository.listVideosByVideoId(videoId, pageable);
    }

    @GetMapping("/addVideoLike/{videoId}")
    @PreAuthorize("isAuthenticated()")
    public void addVideoLike(@PathVariable long videoId, Authentication authentication) {
        SecurityUser securityUser = (SecurityUser) authentication.getPrincipal();
        if (videoRepository.insertVideoLike(securityUser.user().id(), videoId) == 0)
            throw new GracefulResponseException("视频点赞失败,请联系管理员");
    }

    @GetMapping("/removeVideoLike/{videoId}")
    @PreAuthorize("isAuthenticated()")
    public void removeVideoLike(@PathVariable long videoId, Authentication authentication) {
        SecurityUser securityUser = (SecurityUser) authentication.getPrincipal();
        if (videoRepository.deleteVideoLike(securityUser.user().id(), videoId) == 0)
            throw new GracefulResponseException("视频取消点赞失败,请联系管理员");
    }

    @PostMapping("/collectVideo/{videoId}")
    @PreAuthorize("isAuthenticated()")
    public void collectVideo(@PathVariable long videoId, @RequestBody List<Long> collectionIds) {
        if (videoRepository.collectVideo(videoId, collectionIds) == 0)
            throw new GracefulResponseException("视频收藏失败,请联系管理员");
    }

    @GetMapping("/unCollectVideo/{videoId}")
    @PreAuthorize("isAuthenticated()")
    public void unCollectVideo(@PathVariable long videoId, Authentication authentication) {
        SecurityUser securityUser = (SecurityUser) authentication.getPrincipal();
        if (videoRepository.unCollectVideo(securityUser.user().id(), videoId) == 0)
            throw new GracefulResponseException("视频取消收藏失败,请联系管理员");
    }

    @PostMapping("/uploadVideo")
    @PreAuthorize("isAuthenticated()")
    public void uploadVideo(@NotNull MultipartFile source, @NotNull MultipartFile poster, long typeId, String description, Authentication authentication) {
        SecurityUser securityUser = (SecurityUser) authentication.getPrincipal();
        Video video = videoRepository.insertVideo(securityUser.user().id(), typeId, description);
        MinioUtil.uploadFile(source, "video/" + video.id() + ".mp4");
        MinioUtil.uploadFile(poster, "poster/" + video.id() + ".jpg");
        RedisUtil.useSet("type:" + typeId, video.id());
    }

    @GetMapping("/searchVideo")
    public List<Video> searchVideo(String keyword) {
        return videoRepository.searchVideo(keyword);
    }

    @PostMapping("/recordView/{videoId}")
    @PreAuthorize("isAuthenticated()")
    public void recordVideoView(@PathVariable long videoId,
                               @RequestParam int watchDuration,
                               @RequestParam int videoDuration,
                               Authentication authentication) {
        SecurityUser securityUser = (SecurityUser) authentication.getPrincipal();
        videoViewRepository.recordVideoView(securityUser.user().id(), videoId, watchDuration, videoDuration);
    }

    @GetMapping("/viewHistory")
    @PreAuthorize("isAuthenticated()")
    public List<VideoView> getViewHistory(@RequestParam(defaultValue = "20") int limit,
                                         Authentication authentication) {
        SecurityUser securityUser = (SecurityUser) authentication.getPrincipal();
        return videoViewRepository.getUserViewHistory(securityUser.user().id(), limit);
    }

    @GetMapping("/hotVideos")
    public List<Video> getHotVideos(@RequestParam(defaultValue = "20") int count) {
        return videoRepository.findByIds(recommendService.getHotVideos(count), VideoFetcher.$
                .description().likesSum().source().poster().commentsSum().collectionsSum().betweenTime()
                .user(UserFetcher.$.username()));
    }

    @GetMapping("/newVideos")
    public List<Video> getNewVideos(@RequestParam(defaultValue = "20") int count) {
        return videoRepository.findByIds(recommendService.getNewVideos(count), VideoFetcher.$
                .description().likesSum().source().poster().commentsSum().collectionsSum().betweenTime()
                .user(UserFetcher.$.username()));
    }

}

