package com.celeste.entity;

import org.babyfish.jimmer.sql.*;

@Entity
@Table(name = "video_view")
public interface VideoView extends Base {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    long id();

    @ManyToOne
    @JoinColumn(name = "user_id", foreignKeyType = ForeignKeyType.FAKE)
    User user();

    @ManyToOne
    @JoinColumn(name = "video_id", foreignKeyType = ForeignKeyType.FAKE)
    Video video();

    // 观看时长（秒）
    int watchDuration();

    // 视频总时长（秒）
    int videoDuration();

    // 观看完成度（百分比）- 计算属性，不映射到数据库
    @Transient
    default double watchCompletionRate() {
        if (videoDuration() <= 0) return 0.0;
        return Math.min(100.0, (double) watchDuration() / videoDuration() * 100);
    }
}
