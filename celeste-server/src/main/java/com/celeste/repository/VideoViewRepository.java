package com.celeste.repository;

import com.celeste.entity.*;
import org.babyfish.jimmer.spring.repository.JRepository;
import org.babyfish.jimmer.sql.ast.Expression;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public interface VideoViewRepository extends JRepository<VideoView, Long> {

    VideoViewTable table = VideoViewTable.$;

    // 记录用户观看视频
    default VideoView recordVideoView(long userId, long videoId, int watchDuration, int videoDuration) {
        // 检查是否已存在相同的观看记录（同一用户同一视频在短时间内）
        Date oneHourAgo = new Date(System.currentTimeMillis() - 3600000); // 1小时前

        List<VideoView> recentViews = sql().createQuery(table)
                .where(
                    table.user().id().eq(userId)
                    .and(table.video().id().eq(videoId))
                    .and(table.createTime().gt(oneHourAgo))
                )
                .select(table)
                .execute();

        if (!recentViews.isEmpty()) {
            // 更新最近的观看记录
            VideoView existingView = recentViews.get(0);
            return sql().update(VideoViewDraft.$.produce(existingView, draft -> {
                draft.setWatchDuration(Math.max(existingView.watchDuration(), watchDuration));
                draft.setModifyTime(new Date());
            })).getModifiedEntity();
        } else {
            // 创建新的观看记录
            return sql().insert(VideoViewDraft.$.produce(draft -> {
                draft.setUser(UserDraft.$.produce(user -> user.setId(userId)));
                draft.setVideo(VideoDraft.$.produce(video -> video.setId(videoId)));
                draft.setWatchDuration(watchDuration);
                draft.setVideoDuration(videoDuration);
                draft.setCreateTime(new Date());
            })).getModifiedEntity();
        }
    }

    // 获取用户观看历史
    default List<VideoView> getUserViewHistory(long userId, int limit) {
        return sql().createQuery(table)
                .where(table.user().id().eq(userId))
                .orderBy(table.createTime().desc())
                .select(table.fetch(VideoViewFetcher.$
                        .watchDuration()
                        .videoDuration()
                        .createTime()
                        .video(VideoFetcher.$.description().poster().user(UserFetcher.$.username()))
                ))
                .limit(limit)
                .execute();
    }

    // 获取视频的观看统计
    default long getVideoViewCount(long videoId) {
        try {
            return sql().createQuery(table)
                    .where(table.video().id().eq(videoId))
                    .select(Expression.rowCount())
                    .fetchOne();
        } catch (Exception e) {
            // 如果表不存在或查询失败，返回0
            return 0L;
        }
    }

    // 获取用户观看过的视频类型偏好
    default List<Long> getUserPreferredTypes(long userId, int limit) {
        // 先获取用户观看的所有视频ID
        List<Long> videoIds = sql().createQuery(table)
                .where(table.user().id().eq(userId))
                .select(table.video().id())
                .execute();

        if (videoIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 然后查询这些视频的类型，按观看次数排序
        VideoTable videoTable = VideoTable.$;
        return sql().createQuery(videoTable)
                .where(videoTable.id().in(videoIds))
                .groupBy(videoTable.typeId())
                .orderBy(Expression.rowCount().desc())
                .select(videoTable.typeId())
                .limit(limit)
                .execute();
    }

    // 获取用户观看过的视频作者偏好
    default List<Long> getUserPreferredAuthors(long userId, int limit) {
        // 先获取用户观看的所有视频ID
        List<Long> videoIds = sql().createQuery(table)
                .where(table.user().id().eq(userId))
                .select(table.video().id())
                .execute();

        if (videoIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 然后查询这些视频的作者，按观看次数排序
        VideoTable videoTable = VideoTable.$;
        return sql().createQuery(videoTable)
                .where(videoTable.id().in(videoIds))
                .groupBy(videoTable.userId())
                .orderBy(Expression.rowCount().desc())
                .select(videoTable.userId())
                .limit(limit)
                .execute();
    }
}
