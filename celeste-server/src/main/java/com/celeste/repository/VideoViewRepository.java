package com.celeste.repository;

import com.celeste.entity.*;
import org.babyfish.jimmer.spring.repository.JRepository;
import org.babyfish.jimmer.sql.ast.Expression;

import java.util.Date;
import java.util.List;

public interface VideoViewRepository extends JRepository<VideoView, Long> {

    VideoViewTable table = VideoViewTable.$;

    // 记录用户观看视频
    default VideoView recordVideoView(long userId, long videoId, int watchDuration, int videoDuration) {
        // 检查是否已存在相同的观看记录（同一用户同一视频在短时间内）
        Date oneHourAgo = new Date(System.currentTimeMillis() - 3600000); // 1小时前

        List<VideoView> recentViews = sql().createQuery(table)
                .where(
                    table.userId().eq(userId)
                    .and(table.videoId().eq(videoId))
                    .and(table.createTime().gt(oneHourAgo))
                )
                .select(table)
                .execute();

        if (!recentViews.isEmpty()) {
            // 更新最近的观看记录
            VideoView existingView = recentViews.get(0);
            return sql().update(Objects.createVideoView(draft -> draft
                    .setId(existingView.id())
                    .setWatchDuration(Math.max(existingView.watchDuration(), watchDuration))
                    .setModifyTime(new Date())
            )).getModifiedEntity();
        } else {
            // 创建新的观看记录
            return sql().insert(Objects.createVideoView(draft -> draft
                    .setUserId(userId)
                    .setVideoId(videoId)
                    .setWatchDuration(watchDuration)
                    .setVideoDuration(videoDuration)
                    .setCreateTime(new Date())
            )).getModifiedEntity();
        }
    }

    // 获取用户观看历史
    default List<VideoView> getUserViewHistory(long userId, int limit) {
        return sql().createQuery(table)
                .where(table.userId().eq(userId))
                .orderBy(table.createTime().desc())
                .select(table.fetch(VideoViewFetcher.$
                        .watchDuration()
                        .videoDuration()
                        .createTime()
                        .video(VideoFetcher.$.description().poster().user(UserFetcher.$.username()))
                ))
                .limit(limit)
                .execute();
    }

    // 获取视频的观看统计
    default long getVideoViewCount(long videoId) {
        return sql().createQuery(table)
                .where(table.videoId().eq(videoId))
                .select(Expression.rowCount())
                .fetchOne();
    }

    // 获取用户观看过的视频类型偏好
    default List<Long> getUserPreferredTypes(long userId, int limit) {
        VideoTable videoTable = VideoTable.$;
        return sql().createQuery(table)
                .innerJoin(videoTable).on(table.videoId().eq(videoTable.id()))
                .where(table.userId().eq(userId))
                .groupBy(videoTable.typeId())
                .orderBy(Expression.rowCount().desc())
                .select(videoTable.typeId())
                .limit(limit)
                .execute();
    }

    // 获取用户观看过的视频作者偏好
    default List<Long> getUserPreferredAuthors(long userId, int limit) {
        VideoTable videoTable = VideoTable.$;
        return sql().createQuery(table)
                .innerJoin(videoTable).on(table.videoId().eq(videoTable.id()))
                .where(table.userId().eq(userId))
                .groupBy(videoTable.userId())
                .orderBy(Expression.rowCount().desc())
                .select(videoTable.userId())
                .limit(limit)
                .execute();
    }
}
