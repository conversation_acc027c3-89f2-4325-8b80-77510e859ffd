package com.celeste.service;

import cn.hutool.log.StaticLog;
import com.celeste.entity.*;
import com.celeste.repository.VideoRepository;
import com.celeste.repository.VideoViewRepository;
import com.celeste.utils.RedisUtil;
import jakarta.annotation.Resource;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
public class RecommendScheduler {

    @Resource
    private VideoRepository videoRepository;

    @Resource
    private VideoViewRepository videoViewRepository;

    private static final String HOT_VIDEOS_KEY = "hot_videos";
    private static final String NEW_VIDEOS_KEY = "new_videos";

    /**
     * 每小时更新热门视频排行榜
     * 基于点赞数、评论数、收藏数、观看数的综合评分
     */
    @Scheduled(fixedRate = 3600000, initialDelay = 60000) // 每小时执行一次，启动后1分钟开始
    public void updateHotVideos() {
        StaticLog.info("开始更新热门视频排行榜...");

        try {
            VideoTable table = VideoTable.$;
            VideoLikeTable likeTable = VideoLikeTable.$;
            CommentTable commentTable = CommentTable.$;
            CollectionTable collectionTable = CollectionTable.$;

            // 获取所有视频，按创建时间排序（最近的优先）
            List<Video> videos = videoRepository.sql().createQuery(table)
                    .orderBy(table.createTime().desc())
                    .select(table.fetch(VideoFetcher.$
                            .description()
                            .createTime()
                            .likesSum()
                            .commentsSum()
                            .collectionsSum()
                    ))
                    .limit(500) // 取最近的500个视频进行热度计算
                    .execute();

            // 计算每个视频的热度分数并排序
            videos.sort((v1, v2) -> {
                // 计算热度分数：点赞数*2 + 评论数*3 + 收藏数*4
                long score1 = v1.likesSum() * 2 + v1.commentsSum() * 3 + v1.collectionsSum() * 4;
                long score2 = v2.likesSum() * 2 + v2.commentsSum() * 3 + v2.collectionsSum() * 4;

                // 添加观看数到热度计算（如果video_view表存在的话）
                try {
                    score1 += videoViewRepository.getVideoViewCount(v1.id());
                    score2 += videoViewRepository.getVideoViewCount(v2.id());
                } catch (Exception e) {
                    // 如果获取观看数失败（比如表不存在），忽略这部分分数
                    StaticLog.debug("获取视频观看数失败，可能是video_view表不存在: {}", e.getMessage());
                }

                // 时间衰减因子：越新的视频获得额外加分
                long days1 = (System.currentTimeMillis() - v1.createTime().getTime()) / (1000 * 60 * 60 * 24);
                long days2 = (System.currentTimeMillis() - v2.createTime().getTime()) / (1000 * 60 * 60 * 24);
                double timeBonus1 = Math.max(0, 10 - days1 * 0.1);
                double timeBonus2 = Math.max(0, 10 - days2 * 0.1);

                double finalScore1 = score1 + timeBonus1;
                double finalScore2 = score2 + timeBonus2;

                return Double.compare(finalScore2, finalScore1); // 降序排列
            });

            // 清除旧的热门视频缓存
            RedisUtil.delete(HOT_VIDEOS_KEY);

            // 将前100个热门视频ID存入Redis有序集合
            int count = Math.min(100, videos.size());
            for (int i = 0; i < count; i++) {
                Video video = videos.get(i);
                long hotScore = video.likesSum() * 2 + video.commentsSum() * 3 + video.collectionsSum() * 4;

                // 添加观看数到热度计算（如果video_view表存在的话）
                try {
                    long viewCount = videoViewRepository.getVideoViewCount(video.id());
                    hotScore += viewCount;
                } catch (Exception e) {
                    // 如果获取观看数失败（比如表不存在），忽略这部分分数
                    StaticLog.debug("获取视频{}观看数失败: {}", video.id(), e.getMessage());
                }

                // 时间衰减因子
                long daysSinceCreation = (System.currentTimeMillis() - video.createTime().getTime()) / (1000 * 60 * 60 * 24);
                double timeBonus = Math.max(0, 10 - daysSinceCreation * 0.1);

                double finalScore = hotScore + timeBonus;
                RedisUtil.useZSet(HOT_VIDEOS_KEY, video.id(), finalScore);
            }

            // 设置过期时间为2小时
            RedisUtil.expire(HOT_VIDEOS_KEY, 2, TimeUnit.HOURS);

            StaticLog.info("热门视频排行榜更新完成，共更新{}个视频", videos.size());

        } catch (Exception e) {
            StaticLog.error("更新热门视频排行榜失败：{}", e.getMessage());
        }
    }

    /**
     * 每30分钟更新最新视频列表
     */
    @Scheduled(fixedRate = 1800000, initialDelay = 120000) // 每30分钟执行一次，启动后2分钟开始
    public void updateNewVideos() {
        StaticLog.info("开始更新最新视频列表...");

        try {
            VideoTable table = VideoTable.$;

            // 获取最近7天的新视频
            Date sevenDaysAgo = new Date(System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000L);

            List<Video> newVideos = videoRepository.sql().createQuery(table)
                    .where(table.createTime().gt(sevenDaysAgo))
                    .orderBy(table.createTime().desc())
                    .select(table.fetch(VideoFetcher.$.createTime()))
                    .limit(200) // 取前200个新视频
                    .execute();

            // 清除旧的新视频缓存
            RedisUtil.delete(NEW_VIDEOS_KEY);

            // 将新视频ID存入Redis有序集合，分数为创建时间戳
            for (Video video : newVideos) {
                RedisUtil.useZSet(NEW_VIDEOS_KEY, video.id(), video.createTime().getTime());
            }

            // 设置过期时间为1小时
            RedisUtil.expire(NEW_VIDEOS_KEY, 1, TimeUnit.HOURS);

            StaticLog.info("最新视频列表更新完成，共更新{}个视频", newVideos.size());

        } catch (Exception e) {
            StaticLog.error("更新最新视频列表失败：{}", e.getMessage());
        }
    }

    /**
     * 每天凌晨2点清理过期的推荐缓存
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanExpiredCache() {
        StaticLog.info("开始清理过期的推荐缓存...");

        try {
            // 清理用户个性化推荐缓存（保留最近3天的）
            // 这里可以根据需要添加更多缓存清理逻辑

            StaticLog.info("推荐缓存清理完成");

        } catch (Exception e) {
            StaticLog.error("清理推荐缓存失败：{}", e.getMessage());
        }
    }

    /**
     * 每6小时更新视频分类缓存
     */
    @Scheduled(fixedRate = 21600000, initialDelay = 180000) // 每6小时执行一次，启动后3分钟开始
    public void updateVideoCategoryCache() {
        StaticLog.info("开始更新视频分类缓存...");

        try {
            VideoTable table = VideoTable.$;

            // 为每个分类更新视频列表
            for (int typeId = 1; typeId <= 10; typeId++) {
                List<Long> videoIds = videoRepository.sql().createQuery(table)
                        .where(table.typeId().eq((long) typeId))
                        .orderBy(table.createTime().desc())
                        .select(table.id())
                        .execute();

                String typeKey = "type:" + typeId;
                RedisUtil.delete(typeKey);

                for (Long videoId : videoIds) {
                    RedisUtil.useSet(typeKey, videoId);
                }
            }

            StaticLog.info("视频分类缓存更新完成");

        } catch (Exception e) {
            StaticLog.error("更新视频分类缓存失败：{}", e.getMessage());
        }
    }
}
