package com.celeste.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.WeightRandom;
import cn.hutool.core.util.RandomUtil;
import com.celeste.entity.*;
import com.celeste.model.SecurityUser;
import com.celeste.repository.VideoRepository;
import com.celeste.repository.VideoViewRepository;
import com.celeste.utils.RedisUtil;
import jakarta.annotation.Resource;
import org.babyfish.jimmer.sql.ast.Expression;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class RecommendService {

    @Resource
    private VideoRepository videoRepository;

    @Resource
    private VideoViewRepository videoViewRepository;

    public List<Video> recommend(Authentication authentication) {
        Set<Long> videoIds = new LinkedHashSet<>();

        if (authentication == null) {
            // 未登录用户：热门推荐 + 随机推荐
            videoIds.addAll(getRandomizedHotVideos(6));
            videoIds.addAll(unTypeRecommend(4));
        } else {
            SecurityUser securityUser = (SecurityUser) authentication.getPrincipal();
            long userId = securityUser.user().id();

            // 已登录用户：个性化推荐
            videoIds.addAll(getPersonalizedRecommendations(userId, 10));
        }

        // 确保有足够的推荐视频
        if (videoIds.size() < 10) {
            videoIds.addAll(getRandomizedHotVideos(10 - videoIds.size()));
        }

        List<Video> videos = videoRepository.findByIds(videoIds, VideoFetcher.$
                .description().likesSum().source().poster().commentsSum().collectionsSum()
                .userIsAdore().userIsLike().userIsCollect().betweenTime()
                .user(UserFetcher.$.username()));

        // 打乱推荐结果，增加多样性
        Collections.shuffle(videos);
        return videos;
    }

    public Set<Long> unTypeRecommend() {
        return unTypeRecommend(10);
    }

    public Set<Long> unTypeRecommend(int count) {
        Set<Long> videoIds = new HashSet<>();
        for (int i = 1; i <= 10 && videoIds.size() < count; i++) {
            Long videoId = Convert.toLong(RedisUtil.randomMember("type:" + i));
            if (videoId != null) {
                videoIds.add(videoId);
            }
        }
        return videoIds;
    }

    public Set<Long> typeRecommend(List<Long> typeIds) {
        Set<Long> videoIds = new HashSet<>();
        List<WeightRandom.WeightObj<Long>> weightList = new ArrayList<>();
        typeIds.forEach(typeId -> weightList.add(new WeightRandom.WeightObj<>(typeId, 2)));
        for (int i = 0; i < 10 - typeIds.size(); i++) {
            weightList.add(new WeightRandom.WeightObj<>(RandomUtil.randomLong(1, 11), 1));
        }
        WeightRandom<Long> wr = RandomUtil.weightRandom(weightList);
        while (videoIds.size() < 10) {
            Long videoId = Convert.toLong(RedisUtil.randomMember("type:" + wr.next()));
            videoIds.add(videoId);
        }
        return videoIds;
    }

    public List<Video> recommendType(long typeId) {
        Set<Long> videoIds = new HashSet<>();
        while (videoIds.size() < 20) {
            Long videoId = Convert.toLong(RedisUtil.randomMember("type:" + typeId));
            if (videoId != null) {
                videoIds.add(videoId);
            }
        }
        List<Video> videos = videoRepository.findByIds(videoIds, VideoFetcher.$
                .description().likesSum().source().poster().commentsSum().collectionsSum().betweenTime()
                .user(UserFetcher.$.username()));
        Collections.shuffle(videos);
        return videos;
    }

    /**
     * 获取热门视频推荐
     */
    public Set<Long> getHotVideos(int count) {
        Set<String> hotVideoIds = RedisUtil.zRevRange("hot_videos", 0, count - 1);
        return hotVideoIds.stream()
                .map(Convert::toLong)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 获取个性化推荐
     */
    public Set<Long> getPersonalizedRecommendations(long userId, int count) {
        Set<Long> recommendations = new LinkedHashSet<>();

        // 1. 关注的博主新视频推荐 (30%)
        recommendations.addAll(getFollowingUsersNewVideos(userId, (int) (count * 0.3)));

        // 2. 基于观看历史的兴趣推荐 (40%)
        recommendations.addAll(getInterestBasedRecommendations(userId, (int) (count * 0.4)));

        // 3. 热门视频推荐 (20%)
        recommendations.addAll(getHotVideos((int) (count * 0.2)));

        // 4. 新视频推荐 (10%)
        recommendations.addAll(getNewVideos((int) (count * 0.1)));

        return recommendations;
    }

    /**
     * 获取关注用户的新视频
     */
    public Set<Long> getFollowingUsersNewVideos(long userId, int count) {
        if (count <= 0) return new HashSet<>();

        UserTable userTable = UserTable.$;
        VideoTable videoTable = VideoTable.$;

        // 获取用户关注的博主
        List<Long> followingUserIds = videoRepository.sql().createQuery(userTable)
                .where(userTable.followers(follower -> follower.id().eq(userId)))
                .select(userTable.id())
                .execute();

        if (followingUserIds.isEmpty()) {
            return new HashSet<>();
        }

        // 获取关注博主的最新视频（最近7天）
        Date sevenDaysAgo = new Date(System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000L);

        List<Long> newVideoIds = videoRepository.sql().createQuery(videoTable)
                .where(
                    videoTable.userId().in(followingUserIds)
                    .and(videoTable.createTime().gt(sevenDaysAgo))
                )
                .orderBy(videoTable.createTime().desc())
                .select(videoTable.id())
                .limit(count)
                .execute();

        return new HashSet<>(newVideoIds);
    }

    /**
     * 基于兴趣的推荐
     */
    public Set<Long> getInterestBasedRecommendations(long userId, int count) {
        if (count <= 0) return new HashSet<>();

        Set<Long> recommendations = new HashSet<>();

        // 获取用户偏好的视频类型
        List<Long> preferredTypes = videoViewRepository.getUserPreferredTypes(userId, 5);

        // 获取用户偏好的作者
        List<Long> preferredAuthors = videoViewRepository.getUserPreferredAuthors(userId, 3);

        VideoTable videoTable = VideoTable.$;

        // 基于偏好类型推荐
        if (!preferredTypes.isEmpty()) {
            List<Long> typeBasedVideos = videoRepository.sql().createQuery(videoTable)
                    .where(videoTable.typeId().in(preferredTypes))
                    .orderBy(videoTable.createTime().desc()) // 按时间排序，获取最新的
                    .select(videoTable.id())
                    .limit(count / 2)
                    .execute();
            recommendations.addAll(typeBasedVideos);
        }

        // 基于偏好作者推荐
        if (!preferredAuthors.isEmpty() && recommendations.size() < count) {
            List<Long> authorBasedVideos = videoRepository.sql().createQuery(videoTable)
                    .where(videoTable.userId().in(preferredAuthors))
                    .orderBy(videoTable.createTime().desc())
                    .select(videoTable.id())
                    .limit(count - recommendations.size())
                    .execute();
            recommendations.addAll(authorBasedVideos);
        }

        return recommendations;
    }

    /**
     * 获取最新视频
     */
    public Set<Long> getNewVideos(int count) {
        if (count <= 0) return new HashSet<>();

        Set<String> newVideoIds = RedisUtil.zRevRange("new_videos", 0, count - 1);
        return newVideoIds.stream()
                .map(Convert::toLong)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

}
