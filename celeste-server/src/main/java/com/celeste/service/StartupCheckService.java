package com.celeste.service;

import cn.hutool.log.StaticLog;
import com.celeste.utils.RedisUtil;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
public class StartupCheckService {

    @PostConstruct
    public void checkSystemReadiness() {
        StaticLog.info("开始系统启动检查...");

        // 检查Redis连接
        try {
            RedisUtil.useString("startup_check", "ok", 10, TimeUnit.SECONDS);
            Object result = RedisUtil.get("startup_check");
            if ("ok".equals(String.valueOf(result))) {
                StaticLog.info("Redis连接正常");
            } else {
                StaticLog.warn("Redis连接可能有问题");
            }
        } catch (Exception e) {
            StaticLog.error("Redis连接失败: {}", e.getMessage());
        }

        StaticLog.info("系统启动检查完成，推荐系统将在1分钟后开始工作");
    }
}
