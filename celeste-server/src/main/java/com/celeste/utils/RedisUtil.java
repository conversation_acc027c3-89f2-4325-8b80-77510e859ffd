package com.celeste.utils;

import com.celeste.Application;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.util.Set;
import java.util.concurrent.TimeUnit;

public class RedisUtil {

    public final static String JWT_BLACKLIST = "jwt:blacklist:";
    public final static String EMAIL_VERIFY_CODE = "email:verify:code:";
    public final static String EMAIL_VERIFY_LIMIT = "email:verify:limit:";

    private static RedisTemplate<String, Object> template;

    private static RedisTemplate<String, Object> getTemplate() {
        if (template == null) {
            try {
                if (Application.applicationContext != null) {
                    template = Application.applicationContext.getBean("redisTemplate", RedisTemplate.class);
                    template.setKeySerializer(new StringRedisSerializer());
                    template.setValueSerializer(new GenericJackson2JsonRedisSerializer());
                }
            } catch (Exception e) {
                // Redis未初始化或不可用，返回null
                return null;
            }
        }
        return template;
    }

    public static void useString(String key, String value, long timeout, TimeUnit timeUnit) {
        RedisTemplate<String, Object> redisTemplate = getTemplate();
        if (redisTemplate != null) {
            redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
        }
    }

    public static void useSet(String key, Object... value) {
        RedisTemplate<String, Object> redisTemplate = getTemplate();
        if (redisTemplate != null) {
            redisTemplate.opsForSet().add(key, value);
        }
    }

    public static Object get(String key) {
        RedisTemplate<String, Object> redisTemplate = getTemplate();
        return redisTemplate != null ? redisTemplate.opsForValue().get(key) : null;
    }

    public static Object randomMember(String key) {
        RedisTemplate<String, Object> redisTemplate = getTemplate();
        return redisTemplate != null ? redisTemplate.opsForSet().randomMember(key) : null;
    }

    public static void delete(String key) {
        RedisTemplate<String, Object> redisTemplate = getTemplate();
        if (redisTemplate != null) {
            redisTemplate.delete(key);
        }
    }

    public static boolean hasKey(String key) {
        RedisTemplate<String, Object> redisTemplate = getTemplate();
        return redisTemplate != null && Boolean.TRUE.equals(redisTemplate.hasKey(key));
    }

    public static void useZSet(String key, Object value, double score) {
        RedisTemplate<String, Object> redisTemplate = getTemplate();
        if (redisTemplate != null) {
            redisTemplate.opsForZSet().add(key, value, score);
        }
    }

    public static Set<String> zRevRange(String key, long start, long end) {
        RedisTemplate<String, Object> redisTemplate = getTemplate();
        if (redisTemplate != null) {
            Set<Object> result = redisTemplate.opsForZSet().reverseRange(key, start, end);
            if (result != null) {
                return result.stream()
                        .map(Object::toString)
                        .collect(java.util.stream.Collectors.toSet());
            }
        }
        return new java.util.HashSet<>();
    }

    public static void expire(String key, long timeout, TimeUnit timeUnit) {
        RedisTemplate<String, Object> redisTemplate = getTemplate();
        if (redisTemplate != null) {
            redisTemplate.expire(key, timeout, timeUnit);
        }
    }

}
