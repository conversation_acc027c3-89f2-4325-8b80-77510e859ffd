-- 创建视频观看记录表
-- 请在MySQL数据库中执行此脚本

-- 删除表如果存在（重新创建）
DROP TABLE IF EXISTS video_view;

-- 创建video_view表
CREATE TABLE video_view (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    video_id BIGINT NOT NULL COMMENT '视频ID',
    watch_duration INT NOT NULL DEFAULT 0 COMMENT '观看时长（秒）',
    video_duration INT NOT NULL DEFAULT 0 COMMENT '视频总时长（秒）',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',

    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_video_id (video_id),
    INDEX idx_create_time (create_time),
    INDEX idx_user_video (user_id, video_id),
    INDEX idx_user_create_time (user_id, create_time)

    -- 注意：由于使用了ForeignKeyType.FAKE，不添加外键约束
    -- 这样可以避免数据库验证错误，同时保持代码的灵活性
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频观看记录表';
