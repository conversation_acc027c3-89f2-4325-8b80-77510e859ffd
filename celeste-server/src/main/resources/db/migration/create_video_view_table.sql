-- 创建视频观看记录表
-- 请在MySQL数据库中执行此脚本

-- 检查表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS video_view (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    video_id BIGINT NOT NULL,
    watch_duration INT NOT NULL DEFAULT 0 COMMENT '观看时长（秒）',
    video_duration INT NOT NULL DEFAULT 0 COMMENT '视频总时长（秒）',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_user_id (user_id),
    INDEX idx_video_id (video_id),
    INDEX idx_create_time (create_time),
    INDEX idx_user_video (user_id, video_id),
    INDEX idx_user_create_time (user_id, create_time)

    -- 注意：如果user表和video表存在，请取消下面两行的注释
    -- FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    -- <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (video_id) REFERENCES video(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频观看记录表';
