package com.celeste.service;

import com.celeste.entity.Video;
import com.celeste.model.SecurityUser;
import com.celeste.repository.VideoRepository;
import com.celeste.repository.VideoViewRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.Authentication;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RecommendServiceTest {

    @Mock
    private VideoRepository videoRepository;

    @Mock
    private VideoViewRepository videoViewRepository;

    @Mock
    private Authentication authentication;

    @Mock
    private SecurityUser securityUser;

    @InjectMocks
    private RecommendService recommendService;

    @Test
    void testUnTypeRecommend() {
        // 测试未登录用户的推荐
        Set<Long> result = recommendService.unTypeRecommend(5);
        assertNotNull(result);
        assertTrue(result.size() <= 5);
    }

    @Test
    void testGetHotVideos() {
        // 测试热门视频获取
        Set<Long> result = recommendService.getHotVideos(10);
        assertNotNull(result);
        assertTrue(result.size() <= 10);
    }

    @Test
    void testGetNewVideos() {
        // 测试新视频获取
        Set<Long> result = recommendService.getNewVideos(10);
        assertNotNull(result);
        assertTrue(result.size() <= 10);
    }

    @Test
    void testGetPersonalizedRecommendations() {
        // 模拟用户偏好数据
        when(videoViewRepository.getUserPreferredTypes(1L, 5))
                .thenReturn(Arrays.asList(1L, 2L, 3L));
        when(videoViewRepository.getUserPreferredAuthors(1L, 3))
                .thenReturn(Arrays.asList(10L, 20L));

        Set<Long> result = recommendService.getPersonalizedRecommendations(1L, 10);
        assertNotNull(result);
    }

    @Test
    void testGetFollowingUsersNewVideos() {
        // 测试关注用户新视频推荐
        Set<Long> result = recommendService.getFollowingUsersNewVideos(1L, 5);
        assertNotNull(result);
    }

    @Test
    void testGetInterestBasedRecommendations() {
        // 模拟用户兴趣数据
        when(videoViewRepository.getUserPreferredTypes(1L, 5))
                .thenReturn(Arrays.asList(1L, 2L));
        when(videoViewRepository.getUserPreferredAuthors(1L, 3))
                .thenReturn(Arrays.asList(10L));

        Set<Long> result = recommendService.getInterestBasedRecommendations(1L, 5);
        assertNotNull(result);
    }
}
