package com.celeste.entity;

import org.babyfish.jimmer.internal.GeneratedBy;

@GeneratedBy
public interface Fetchers {
    AuthorityFetcher AUTHORITY_FETCHER = AuthorityFetcher.$;

    CollectionFetcher COLLECTION_FETCHER = CollectionFetcher.$;

    CommentFetcher COMMENT_FETCHER = CommentFetcher.$;

    CommentLikeFetcher COMMENT_LIKE_FETCHER = CommentLikeFetcher.$;

    MessageFetcher MESSAGE_FETCHER = MessageFetcher.$;

    RoleFetcher ROLE_FETCHER = RoleFetcher.$;

    TypeFetcher TYPE_FETCHER = TypeFetcher.$;

    UserFetcher USER_FETCHER = UserFetcher.$;

    VideoFetcher VIDEO_FETCHER = VideoFetcher.$;

    VideoLikeFetcher VIDEO_LIKE_FETCHER = VideoLikeFetcher.$;

    VideoViewFetcher VIDEO_VIEW_FETCHER = VideoViewFetcher.$;
}
