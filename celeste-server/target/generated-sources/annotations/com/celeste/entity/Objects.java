package com.celeste.entity;

import org.babyfish.jimmer.DraftConsumer;
import org.babyfish.jimmer.internal.GeneratedBy;

@GeneratedBy
public interface Objects {
    static Authority createAuthority(DraftConsumer<AuthorityDraft> block) {
        return AuthorityDraft.$.produce(block);
    }

    static Authority createAuthority(Authority base, DraftConsumer<AuthorityDraft> block) {
        return AuthorityDraft.$.produce(base, block);
    }

    static Collection createCollection(DraftConsumer<CollectionDraft> block) {
        return CollectionDraft.$.produce(block);
    }

    static Collection createCollection(Collection base, DraftConsumer<CollectionDraft> block) {
        return CollectionDraft.$.produce(base, block);
    }

    static Comment createComment(DraftConsumer<CommentDraft> block) {
        return CommentDraft.$.produce(block);
    }

    static Comment createComment(Comment base, DraftConsumer<CommentDraft> block) {
        return CommentDraft.$.produce(base, block);
    }

    static CommentLike createCommentLike(DraftConsumer<CommentLikeDraft> block) {
        return CommentLikeDraft.$.produce(block);
    }

    static CommentLike createCommentLike(CommentLike base, DraftConsumer<CommentLikeDraft> block) {
        return CommentLikeDraft.$.produce(base, block);
    }

    static Message createMessage(DraftConsumer<MessageDraft> block) {
        return MessageDraft.$.produce(block);
    }

    static Message createMessage(Message base, DraftConsumer<MessageDraft> block) {
        return MessageDraft.$.produce(base, block);
    }

    static Role createRole(DraftConsumer<RoleDraft> block) {
        return RoleDraft.$.produce(block);
    }

    static Role createRole(Role base, DraftConsumer<RoleDraft> block) {
        return RoleDraft.$.produce(base, block);
    }

    static Type createType(DraftConsumer<TypeDraft> block) {
        return TypeDraft.$.produce(block);
    }

    static Type createType(Type base, DraftConsumer<TypeDraft> block) {
        return TypeDraft.$.produce(base, block);
    }

    static User createUser(DraftConsumer<UserDraft> block) {
        return UserDraft.$.produce(block);
    }

    static User createUser(User base, DraftConsumer<UserDraft> block) {
        return UserDraft.$.produce(base, block);
    }

    static Video createVideo(DraftConsumer<VideoDraft> block) {
        return VideoDraft.$.produce(block);
    }

    static Video createVideo(Video base, DraftConsumer<VideoDraft> block) {
        return VideoDraft.$.produce(base, block);
    }

    static VideoLike createVideoLike(DraftConsumer<VideoLikeDraft> block) {
        return VideoLikeDraft.$.produce(block);
    }

    static VideoLike createVideoLike(VideoLike base, DraftConsumer<VideoLikeDraft> block) {
        return VideoLikeDraft.$.produce(base, block);
    }

    static VideoView createVideoView(DraftConsumer<VideoViewDraft> block) {
        return VideoViewDraft.$.produce(block);
    }

    static VideoView createVideoView(VideoView base, DraftConsumer<VideoViewDraft> block) {
        return VideoViewDraft.$.produce(base, block);
    }
}
