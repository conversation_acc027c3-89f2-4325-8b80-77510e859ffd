package com.celeste.entity;

import org.babyfish.jimmer.internal.GeneratedBy;

@GeneratedBy
public interface TableExes {
    AuthorityTableEx AUTHORITY_TABLE_EX = AuthorityTableEx.$;

    CollectionTableEx COLLECTION_TABLE_EX = CollectionTableEx.$;

    CommentTableEx COMMENT_TABLE_EX = CommentTableEx.$;

    CommentLikeTableEx COMMENT_LIKE_TABLE_EX = CommentLikeTableEx.$;

    MessageTableEx MESSAGE_TABLE_EX = MessageTableEx.$;

    RoleTableEx ROLE_TABLE_EX = RoleTableEx.$;

    TypeTableEx TYPE_TABLE_EX = TypeTableEx.$;

    UserTableEx USER_TABLE_EX = UserTableEx.$;

    VideoTableEx VIDEO_TABLE_EX = VideoTableEx.$;

    VideoLikeTableEx VIDEO_LIKE_TABLE_EX = VideoLikeTableEx.$;

    VideoViewTableEx VIDEO_VIEW_TABLE_EX = VideoViewTableEx.$;
}
