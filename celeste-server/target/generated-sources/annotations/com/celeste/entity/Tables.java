package com.celeste.entity;

import org.babyfish.jimmer.internal.GeneratedBy;

@GeneratedBy
public interface Tables {
    AuthorityTable AUTHORITY_TABLE = AuthorityTable.$;

    CollectionTable COLLECTION_TABLE = CollectionTable.$;

    CommentTable COMMENT_TABLE = CommentTable.$;

    CommentLikeTable COMMENT_LIKE_TABLE = CommentLikeTable.$;

    MessageTable MESSAGE_TABLE = MessageTable.$;

    RoleTable ROLE_TABLE = RoleTable.$;

    TypeTable TYPE_TABLE = TypeTable.$;

    UserTable USER_TABLE = UserTable.$;

    VideoTable VIDEO_TABLE = VideoTable.$;

    VideoLikeTable VIDEO_LIKE_TABLE = VideoLikeTable.$;

    VideoViewTable VIDEO_VIEW_TABLE = VideoViewTable.$;
}
