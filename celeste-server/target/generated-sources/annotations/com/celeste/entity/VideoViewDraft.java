package com.celeste.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.io.Serializable;
import java.lang.CloneNotSupportedException;
import java.lang.Cloneable;
import java.lang.Double;
import java.lang.IllegalArgumentException;
import java.lang.IllegalStateException;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.lang.System;
import java.util.Collections;
import java.util.Date;
import java.util.Objects;
import org.babyfish.jimmer.CircularReferenceException;
import org.babyfish.jimmer.DraftConsumer;
import org.babyfish.jimmer.ImmutableObjects;
import org.babyfish.jimmer.UnloadedException;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.jackson.ImmutableModuleRequiredException;
import org.babyfish.jimmer.lang.OldChain;
import org.babyfish.jimmer.meta.ImmutablePropCategory;
import org.babyfish.jimmer.meta.ImmutableType;
import org.babyfish.jimmer.meta.PropId;
import org.babyfish.jimmer.runtime.DraftContext;
import org.babyfish.jimmer.runtime.DraftSpi;
import org.babyfish.jimmer.runtime.ImmutableSpi;
import org.babyfish.jimmer.runtime.Internal;
import org.babyfish.jimmer.runtime.Visibility;
import org.babyfish.jimmer.sql.ManyToOne;
import org.jetbrains.annotations.Nullable;

@GeneratedBy(
        type = VideoView.class
)
public interface VideoViewDraft extends VideoView, BaseDraft {
    VideoViewDraft.Producer $ = Producer.INSTANCE;

    @OldChain
    VideoViewDraft setId(long id);

    @OldChain
    VideoViewDraft setCreateTime(Date createTime);

    @OldChain
    VideoViewDraft setModifyTime(Date modifyTime);

    @Nullable
    UserDraft user();

    UserDraft user(boolean autoCreate);

    @OldChain
    VideoViewDraft setUser(User user);

    @Nullable
    Long userId();

    @OldChain
    VideoViewDraft setUserId(@Nullable Long userId);

    @OldChain
    VideoViewDraft applyUser(DraftConsumer<UserDraft> block);

    @OldChain
    VideoViewDraft applyUser(User base, DraftConsumer<UserDraft> block);

    @Nullable
    VideoDraft video();

    VideoDraft video(boolean autoCreate);

    @OldChain
    VideoViewDraft setVideo(Video video);

    @Nullable
    Long videoId();

    @OldChain
    VideoViewDraft setVideoId(@Nullable Long videoId);

    @OldChain
    VideoViewDraft applyVideo(DraftConsumer<VideoDraft> block);

    @OldChain
    VideoViewDraft applyVideo(Video base, DraftConsumer<VideoDraft> block);

    @OldChain
    VideoViewDraft setWatchDuration(int watchDuration);

    @OldChain
    VideoViewDraft setVideoDuration(int videoDuration);

    @GeneratedBy(
            type = VideoView.class
    )
    class Producer {
        static final Producer INSTANCE = new Producer();

        public static final int SLOT_ID = 3;

        public static final int SLOT_CREATE_TIME = 0;

        public static final int SLOT_MODIFY_TIME = 1;

        public static final int SLOT_BETWEEN_TIME = 2;

        public static final int SLOT_USER = 4;

        public static final int SLOT_VIDEO = 5;

        public static final int SLOT_WATCH_DURATION = 6;

        public static final int SLOT_VIDEO_DURATION = 7;

        public static final int SLOT_WATCH_COMPLETION_RATE = 8;

        public static final ImmutableType TYPE = ImmutableType
            .newBuilder(
                "0.8.150",
                VideoView.class,
                Collections.singleton(BaseDraft.Producer.TYPE),
                (ctx, base) -> new DraftImpl(ctx, (VideoView)base)
            )
            .redefine("createTime", SLOT_CREATE_TIME)
            .redefine("modifyTime", SLOT_MODIFY_TIME)
            .redefine("betweenTime", SLOT_BETWEEN_TIME)
            .id(SLOT_ID, "id", long.class)
            .add(SLOT_USER, "user", ManyToOne.class, User.class, true)
            .add(SLOT_VIDEO, "video", ManyToOne.class, Video.class, true)
            .add(SLOT_WATCH_DURATION, "watchDuration", ImmutablePropCategory.SCALAR, int.class, false)
            .add(SLOT_VIDEO_DURATION, "videoDuration", ImmutablePropCategory.SCALAR, int.class, false)
            .add(SLOT_WATCH_COMPLETION_RATE, "watchCompletionRate", ImmutablePropCategory.SCALAR, double.class, false)
            .build();

        private Producer() {
        }

        public VideoView produce(DraftConsumer<VideoViewDraft> block) {
            return produce(null, block);
        }

        public VideoView produce(VideoView base, DraftConsumer<VideoViewDraft> block) {
            return (VideoView)Internal.produce(TYPE, base, block);
        }

        @GeneratedBy(
                type = VideoView.class
        )
        @JsonPropertyOrder({"dummyPropForJacksonError__", "createTime", "modifyTime", "betweenTime", "id", "user", "video", "watchDuration", "videoDuration", "watchCompletionRate"})
        public abstract interface Implementor extends VideoView, ImmutableSpi {
            @Override
            default Object __get(PropId prop) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __get(prop.asName());
                    case SLOT_CREATE_TIME:
                    		return createTime();
                    case SLOT_MODIFY_TIME:
                    		return modifyTime();
                    case SLOT_BETWEEN_TIME:
                    		return betweenTime();
                    case SLOT_ID:
                    		return (Long)id();
                    case SLOT_USER:
                    		return user();
                    case SLOT_VIDEO:
                    		return video();
                    case SLOT_WATCH_DURATION:
                    		return (Integer)watchDuration();
                    case SLOT_VIDEO_DURATION:
                    		return (Integer)videoDuration();
                    case SLOT_WATCH_COMPLETION_RATE:
                    		return (Double)watchCompletionRate();
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.VideoView\": \"" + prop + "\"");
                }
            }

            @Override
            default Object __get(String prop) {
                switch (prop) {
                    case "createTime":
                    		return createTime();
                    case "modifyTime":
                    		return modifyTime();
                    case "betweenTime":
                    		return betweenTime();
                    case "id":
                    		return (Long)id();
                    case "user":
                    		return user();
                    case "video":
                    		return video();
                    case "watchDuration":
                    		return (Integer)watchDuration();
                    case "videoDuration":
                    		return (Integer)videoDuration();
                    case "watchCompletionRate":
                    		return (Double)watchCompletionRate();
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.VideoView\": \"" + prop + "\"");
                }
            }

            default long getId() {
                return id();
            }

            default Date getCreateTime() {
                return createTime();
            }

            @Nullable
            default Date getModifyTime() {
                return modifyTime();
            }

            default String getBetweenTime() {
                return betweenTime();
            }

            @Nullable
            default User getUser() {
                return user();
            }

            @Nullable
            default Video getVideo() {
                return video();
            }

            default int getWatchDuration() {
                return watchDuration();
            }

            default int getVideoDuration() {
                return videoDuration();
            }

            default double getWatchCompletionRate() {
                return watchCompletionRate();
            }

            @Override
            default ImmutableType __type() {
                return TYPE;
            }

            default int getDummyPropForJacksonError__() {
                throw new ImmutableModuleRequiredException();
            }
        }

        @GeneratedBy(
                type = VideoView.class
        )
        private static class Impl implements Implementor, Cloneable, Serializable {
            private Visibility __visibility;

            long __idValue;

            boolean __idLoaded = false;

            Date __createTimeValue;

            Date __modifyTimeValue;

            boolean __modifyTimeLoaded = false;

            User __userValue;

            boolean __userLoaded = false;

            Video __videoValue;

            boolean __videoLoaded = false;

            int __watchDurationValue;

            boolean __watchDurationLoaded = false;

            int __videoDurationValue;

            boolean __videoDurationLoaded = false;

            Impl() {
                __visibility = Visibility.of(9);
                __visibility.show(SLOT_BETWEEN_TIME, false);
                __visibility.show(SLOT_WATCH_COMPLETION_RATE, false);
            }

            @Override
            @JsonIgnore
            public long id() {
                if (!__idLoaded) {
                    throw new UnloadedException(VideoView.class, "id");
                }
                return __idValue;
            }

            @Override
            @JsonIgnore
            public Date createTime() {
                if (__createTimeValue == null) {
                    throw new UnloadedException(VideoView.class, "createTime");
                }
                return __createTimeValue;
            }

            @Override
            @JsonIgnore
            @Nullable
            public Date modifyTime() {
                if (!__modifyTimeLoaded) {
                    throw new UnloadedException(VideoView.class, "modifyTime");
                }
                return __modifyTimeValue;
            }

            @Override
            @JsonIgnore
            @Nullable
            public User user() {
                if (!__userLoaded) {
                    throw new UnloadedException(VideoView.class, "user");
                }
                return __userValue;
            }

            @Override
            @JsonIgnore
            @Nullable
            public Video video() {
                if (!__videoLoaded) {
                    throw new UnloadedException(VideoView.class, "video");
                }
                return __videoValue;
            }

            @Override
            @JsonIgnore
            public int watchDuration() {
                if (!__watchDurationLoaded) {
                    throw new UnloadedException(VideoView.class, "watchDuration");
                }
                return __watchDurationValue;
            }

            @Override
            @JsonIgnore
            public int videoDuration() {
                if (!__videoDurationLoaded) {
                    throw new UnloadedException(VideoView.class, "videoDuration");
                }
                return __videoDurationValue;
            }

            @Override
            public Impl clone() {
                try {
                    return (Impl)super.clone();
                } catch(CloneNotSupportedException ex) {
                    throw new AssertionError(ex);
                }
            }

            @Override
            public boolean __isLoaded(PropId prop) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __isLoaded(prop.asName());
                    case SLOT_CREATE_TIME:
                    		return __createTimeValue != null;
                    case SLOT_MODIFY_TIME:
                    		return __modifyTimeLoaded;
                    case SLOT_BETWEEN_TIME:
                    		return __isLoaded(PropId.byIndex(SLOT_CREATE_TIME));
                    case SLOT_ID:
                    		return __idLoaded;
                    case SLOT_USER:
                    		return __userLoaded;
                    case SLOT_VIDEO:
                    		return __videoLoaded;
                    case SLOT_WATCH_DURATION:
                    		return __watchDurationLoaded;
                    case SLOT_VIDEO_DURATION:
                    		return __videoDurationLoaded;
                    case SLOT_WATCH_COMPLETION_RATE:
                    		return __isLoaded(PropId.byIndex(SLOT_WATCH_DURATION)) && 
                        __isLoaded(PropId.byIndex(SLOT_VIDEO_DURATION));
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.VideoView\": \"" + prop + "\"");
                }
            }

            @Override
            public boolean __isLoaded(String prop) {
                switch (prop) {
                    case "createTime":
                    		return __createTimeValue != null;
                    case "modifyTime":
                    		return __modifyTimeLoaded;
                    case "betweenTime":
                    		return __isLoaded(PropId.byIndex(SLOT_CREATE_TIME));
                    case "id":
                    		return __idLoaded;
                    case "user":
                    		return __userLoaded;
                    case "video":
                    		return __videoLoaded;
                    case "watchDuration":
                    		return __watchDurationLoaded;
                    case "videoDuration":
                    		return __videoDurationLoaded;
                    case "watchCompletionRate":
                    		return __isLoaded(PropId.byIndex(SLOT_WATCH_DURATION)) && 
                        __isLoaded(PropId.byIndex(SLOT_VIDEO_DURATION));
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.VideoView\": \"" + prop + "\"");
                }
            }

            @Override
            public boolean __isVisible(PropId prop) {
                if (__visibility == null) {
                    return true;
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		return __isVisible(prop.asName());
                    case SLOT_CREATE_TIME:
                    		return __visibility.visible(SLOT_CREATE_TIME);
                    case SLOT_MODIFY_TIME:
                    		return __visibility.visible(SLOT_MODIFY_TIME);
                    case SLOT_BETWEEN_TIME:
                    		return __visibility.visible(SLOT_BETWEEN_TIME);
                    case SLOT_ID:
                    		return __visibility.visible(SLOT_ID);
                    case SLOT_USER:
                    		return __visibility.visible(SLOT_USER);
                    case SLOT_VIDEO:
                    		return __visibility.visible(SLOT_VIDEO);
                    case SLOT_WATCH_DURATION:
                    		return __visibility.visible(SLOT_WATCH_DURATION);
                    case SLOT_VIDEO_DURATION:
                    		return __visibility.visible(SLOT_VIDEO_DURATION);
                    case SLOT_WATCH_COMPLETION_RATE:
                    		return __visibility.visible(SLOT_WATCH_COMPLETION_RATE);
                    default: return true;
                }
            }

            @Override
            public boolean __isVisible(String prop) {
                if (__visibility == null) {
                    return true;
                }
                switch (prop) {
                    case "createTime":
                    		return __visibility.visible(SLOT_CREATE_TIME);
                    case "modifyTime":
                    		return __visibility.visible(SLOT_MODIFY_TIME);
                    case "betweenTime":
                    		return __visibility.visible(SLOT_BETWEEN_TIME);
                    case "id":
                    		return __visibility.visible(SLOT_ID);
                    case "user":
                    		return __visibility.visible(SLOT_USER);
                    case "video":
                    		return __visibility.visible(SLOT_VIDEO);
                    case "watchDuration":
                    		return __visibility.visible(SLOT_WATCH_DURATION);
                    case "videoDuration":
                    		return __visibility.visible(SLOT_VIDEO_DURATION);
                    case "watchCompletionRate":
                    		return __visibility.visible(SLOT_WATCH_COMPLETION_RATE);
                    default: return true;
                }
            }

            @Override
            public int hashCode() {
                int hash = __visibility != null ? __visibility.hashCode() : 0;
                if (__idLoaded) {
                    hash = 31 * hash + Long.hashCode(__idValue);
                    // If entity-id is loaded, return directly
                    return hash;
                }
                if (__createTimeValue != null) {
                    hash = 31 * hash + __createTimeValue.hashCode();
                }
                if (__modifyTimeLoaded && __modifyTimeValue != null) {
                    hash = 31 * hash + __modifyTimeValue.hashCode();
                }
                if (__userLoaded && __userValue != null) {
                    hash = 31 * hash + __userValue.hashCode();
                }
                if (__videoLoaded && __videoValue != null) {
                    hash = 31 * hash + __videoValue.hashCode();
                }
                if (__watchDurationLoaded) {
                    hash = 31 * hash + Integer.hashCode(__watchDurationValue);
                }
                if (__videoDurationLoaded) {
                    hash = 31 * hash + Integer.hashCode(__videoDurationValue);
                }
                return hash;
            }

            private int __shallowHashCode() {
                int hash = __visibility != null ? __visibility.hashCode() : 0;
                if (__idLoaded) {
                    hash = 31 * hash + Long.hashCode(__idValue);
                }
                if (__createTimeValue != null) {
                    hash = 31 * hash + System.identityHashCode(__createTimeValue);
                }
                if (__modifyTimeLoaded) {
                    hash = 31 * hash + System.identityHashCode(__modifyTimeValue);
                }
                if (__userLoaded) {
                    hash = 31 * hash + System.identityHashCode(__userValue);
                }
                if (__videoLoaded) {
                    hash = 31 * hash + System.identityHashCode(__videoValue);
                }
                if (__watchDurationLoaded) {
                    hash = 31 * hash + Integer.hashCode(__watchDurationValue);
                }
                if (__videoDurationLoaded) {
                    hash = 31 * hash + Integer.hashCode(__videoDurationValue);
                }
                return hash;
            }

            @Override
            public int __hashCode(boolean shallow) {
                return shallow ? __shallowHashCode() : hashCode();
            }

            @Override
            public boolean equals(Object obj) {
                if (obj == null || !(obj instanceof Implementor)) {
                    return false;
                }
                Implementor __other = (Implementor)obj;
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                boolean __idLoaded = this.__idLoaded;
                if (__idLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                if (__idLoaded) {
                    // If entity-id is loaded, return directly
                    return __idValue == __other.id();
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATE_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                boolean __createTimeLoaded = __createTimeValue != null;
                if (__createTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                if (__createTimeLoaded && !Objects.equals(__createTimeValue, __other.createTime())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_MODIFY_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                boolean __modifyTimeLoaded = this.__modifyTimeLoaded;
                if (__modifyTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                if (__modifyTimeLoaded && !Objects.equals(__modifyTimeValue, __other.modifyTime())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USER)) != __other.__isVisible(PropId.byIndex(SLOT_USER))) {
                    return false;
                }
                boolean __userLoaded = this.__userLoaded;
                if (__userLoaded != __other.__isLoaded(PropId.byIndex(SLOT_USER))) {
                    return false;
                }
                if (__userLoaded && !Objects.equals(__userValue, __other.user())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_VIDEO)) != __other.__isVisible(PropId.byIndex(SLOT_VIDEO))) {
                    return false;
                }
                boolean __videoLoaded = this.__videoLoaded;
                if (__videoLoaded != __other.__isLoaded(PropId.byIndex(SLOT_VIDEO))) {
                    return false;
                }
                if (__videoLoaded && !Objects.equals(__videoValue, __other.video())) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_WATCH_DURATION)) != __other.__isVisible(PropId.byIndex(SLOT_WATCH_DURATION))) {
                    return false;
                }
                boolean __watchDurationLoaded = this.__watchDurationLoaded;
                if (__watchDurationLoaded != __other.__isLoaded(PropId.byIndex(SLOT_WATCH_DURATION))) {
                    return false;
                }
                if (__watchDurationLoaded && __watchDurationValue != __other.watchDuration()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_VIDEO_DURATION)) != __other.__isVisible(PropId.byIndex(SLOT_VIDEO_DURATION))) {
                    return false;
                }
                boolean __videoDurationLoaded = this.__videoDurationLoaded;
                if (__videoDurationLoaded != __other.__isLoaded(PropId.byIndex(SLOT_VIDEO_DURATION))) {
                    return false;
                }
                if (__videoDurationLoaded && __videoDurationValue != __other.videoDuration()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_WATCH_COMPLETION_RATE)) != __other.__isVisible(PropId.byIndex(SLOT_WATCH_COMPLETION_RATE))) {
                    return false;
                }
                return true;
            }

            private boolean __shallowEquals(Object obj) {
                if (obj == null || !(obj instanceof Implementor)) {
                    return false;
                }
                Implementor __other = (Implementor)obj;
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                boolean __idLoaded = this.__idLoaded;
                if (__idLoaded != __other.__isLoaded(PropId.byIndex(SLOT_ID))) {
                    return false;
                }
                if (__idLoaded && __idValue != __other.id()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATE_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                boolean __createTimeLoaded = __createTimeValue != null;
                if (__createTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false;
                }
                if (__createTimeLoaded && __createTimeValue != __other.createTime()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_MODIFY_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                boolean __modifyTimeLoaded = this.__modifyTimeLoaded;
                if (__modifyTimeLoaded != __other.__isLoaded(PropId.byIndex(SLOT_MODIFY_TIME))) {
                    return false;
                }
                if (__modifyTimeLoaded && __modifyTimeValue != __other.modifyTime()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_BETWEEN_TIME))) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_USER)) != __other.__isVisible(PropId.byIndex(SLOT_USER))) {
                    return false;
                }
                boolean __userLoaded = this.__userLoaded;
                if (__userLoaded != __other.__isLoaded(PropId.byIndex(SLOT_USER))) {
                    return false;
                }
                if (__userLoaded && __userValue != __other.user()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_VIDEO)) != __other.__isVisible(PropId.byIndex(SLOT_VIDEO))) {
                    return false;
                }
                boolean __videoLoaded = this.__videoLoaded;
                if (__videoLoaded != __other.__isLoaded(PropId.byIndex(SLOT_VIDEO))) {
                    return false;
                }
                if (__videoLoaded && __videoValue != __other.video()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_WATCH_DURATION)) != __other.__isVisible(PropId.byIndex(SLOT_WATCH_DURATION))) {
                    return false;
                }
                boolean __watchDurationLoaded = this.__watchDurationLoaded;
                if (__watchDurationLoaded != __other.__isLoaded(PropId.byIndex(SLOT_WATCH_DURATION))) {
                    return false;
                }
                if (__watchDurationLoaded && __watchDurationValue != __other.watchDuration()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_VIDEO_DURATION)) != __other.__isVisible(PropId.byIndex(SLOT_VIDEO_DURATION))) {
                    return false;
                }
                boolean __videoDurationLoaded = this.__videoDurationLoaded;
                if (__videoDurationLoaded != __other.__isLoaded(PropId.byIndex(SLOT_VIDEO_DURATION))) {
                    return false;
                }
                if (__videoDurationLoaded && __videoDurationValue != __other.videoDuration()) {
                    return false;
                }
                if (__isVisible(PropId.byIndex(SLOT_WATCH_COMPLETION_RATE)) != __other.__isVisible(PropId.byIndex(SLOT_WATCH_COMPLETION_RATE))) {
                    return false;
                }
                return true;
            }

            @Override
            public boolean __equals(Object obj, boolean shallow) {
                return shallow ? __shallowEquals(obj) : equals(obj);
            }

            @Override
            public String toString() {
                return ImmutableObjects.toString(this);
            }
        }

        @GeneratedBy(
                type = VideoView.class
        )
        private static class DraftImpl implements Implementor, DraftSpi, VideoViewDraft {
            private DraftContext __ctx;

            private Impl __base;

            private Impl __modified;

            private boolean __resolving;

            private VideoView __resolved;

            DraftImpl(DraftContext ctx, VideoView base) {
                __ctx = ctx;
                if (base != null) {
                    __base = (Impl)base;
                }
                else {
                    __modified = new Impl();
                }
            }

            @Override
            public boolean __isLoaded(PropId prop) {
                return (__modified!= null ? __modified : __base).__isLoaded(prop);
            }

            @Override
            public boolean __isLoaded(String prop) {
                return (__modified!= null ? __modified : __base).__isLoaded(prop);
            }

            @Override
            public boolean __isVisible(PropId prop) {
                return (__modified!= null ? __modified : __base).__isVisible(prop);
            }

            @Override
            public boolean __isVisible(String prop) {
                return (__modified!= null ? __modified : __base).__isVisible(prop);
            }

            @Override
            public int hashCode() {
                return (__modified!= null ? __modified : __base).hashCode();
            }

            @Override
            public int __hashCode(boolean shallow) {
                return (__modified!= null ? __modified : __base).__hashCode(shallow);
            }

            @Override
            public boolean equals(Object obj) {
                return (__modified!= null ? __modified : __base).equals(obj);
            }

            @Override
            public boolean __equals(Object obj, boolean shallow) {
                return (__modified!= null ? __modified : __base).__equals(obj, shallow);
            }

            @Override
            public String toString() {
                return ImmutableObjects.toString(this);
            }

            @Override
            @JsonIgnore
            public long id() {
                return (__modified!= null ? __modified : __base).id();
            }

            @Override
            public VideoViewDraft setId(long id) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__idValue = id;
                __tmpModified.__idLoaded = true;
                return this;
            }

            @Override
            @JsonIgnore
            public Date createTime() {
                return (__modified!= null ? __modified : __base).createTime();
            }

            @Override
            public VideoViewDraft setCreateTime(Date createTime) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                if (createTime == null) {
                    throw new IllegalArgumentException(
                        "'createTime' cannot be null, please specify non-null value or use nullable annotation to decorate this property"
                    );
                }
                Impl __tmpModified = __modified();
                __tmpModified.__createTimeValue = createTime;
                return this;
            }

            @Override
            @JsonIgnore
            @Nullable
            public Date modifyTime() {
                return (__modified!= null ? __modified : __base).modifyTime();
            }

            @Override
            public VideoViewDraft setModifyTime(Date modifyTime) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__modifyTimeValue = modifyTime;
                __tmpModified.__modifyTimeLoaded = true;
                return this;
            }

            @Override
            @JsonIgnore
            public String betweenTime() {
                return (__modified!= null ? __modified : __base).betweenTime();
            }

            @Override
            @JsonIgnore
            @Nullable
            public UserDraft user() {
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).user());
            }

            @Override
            public UserDraft user(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_USER)) || user() == null)) {
                    setUser(UserDraft.$.produce(null, null));
                }
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).user());
            }

            @Override
            public VideoViewDraft setUser(User user) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__userValue = user;
                __tmpModified.__userLoaded = true;
                return this;
            }

            @Nullable
            @Override
            public Long userId() {
                User user = user();
                if (user == null) {
                    return null;
                }
                return user.id();
            }

            @OldChain
            @Override
            public VideoViewDraft setUserId(@Nullable Long userId) {
                if (userId == null) {
                    setUser(null);
                    return this;
                }
                user(true).setId(userId);
                return this;
            }

            @Override
            public VideoViewDraft applyUser(DraftConsumer<UserDraft> block) {
                applyUser(null, block);
                return this;
            }

            @Override
            public VideoViewDraft applyUser(User base, DraftConsumer<UserDraft> block) {
                setUser(UserDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            @Nullable
            public VideoDraft video() {
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).video());
            }

            @Override
            public VideoDraft video(boolean autoCreate) {
                if (autoCreate && (!__isLoaded(PropId.byIndex(SLOT_VIDEO)) || video() == null)) {
                    setVideo(VideoDraft.$.produce(null, null));
                }
                return __ctx.toDraftObject((__modified!= null ? __modified : __base).video());
            }

            @Override
            public VideoViewDraft setVideo(Video video) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__videoValue = video;
                __tmpModified.__videoLoaded = true;
                return this;
            }

            @Nullable
            @Override
            public Long videoId() {
                Video video = video();
                if (video == null) {
                    return null;
                }
                return video.id();
            }

            @OldChain
            @Override
            public VideoViewDraft setVideoId(@Nullable Long videoId) {
                if (videoId == null) {
                    setVideo(null);
                    return this;
                }
                video(true).setId(videoId);
                return this;
            }

            @Override
            public VideoViewDraft applyVideo(DraftConsumer<VideoDraft> block) {
                applyVideo(null, block);
                return this;
            }

            @Override
            public VideoViewDraft applyVideo(Video base, DraftConsumer<VideoDraft> block) {
                setVideo(VideoDraft.$.produce(base, block));
                return this;
            }

            @Override
            @JsonIgnore
            public int watchDuration() {
                return (__modified!= null ? __modified : __base).watchDuration();
            }

            @Override
            public VideoViewDraft setWatchDuration(int watchDuration) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__watchDurationValue = watchDuration;
                __tmpModified.__watchDurationLoaded = true;
                return this;
            }

            @Override
            @JsonIgnore
            public int videoDuration() {
                return (__modified!= null ? __modified : __base).videoDuration();
            }

            @Override
            public VideoViewDraft setVideoDuration(int videoDuration) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Impl __tmpModified = __modified();
                __tmpModified.__videoDurationValue = videoDuration;
                __tmpModified.__videoDurationLoaded = true;
                return this;
            }

            @Override
            @JsonIgnore
            public double watchCompletionRate() {
                return (__modified!= null ? __modified : __base).watchCompletionRate();
            }

            @SuppressWarnings("unchecked")
            @Override
            public void __set(PropId prop, Object value) {
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__set(prop.asName(), value);
                    return;
                    case SLOT_CREATE_TIME:
                    		setCreateTime((Date)value);break;
                    case SLOT_MODIFY_TIME:
                    		setModifyTime((Date)value);break;
                    case SLOT_BETWEEN_TIME:
                    		break;
                    case SLOT_ID:
                    		if (value == null) throw new IllegalArgumentException("'id' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setId((Long)value);
                            break;
                    case SLOT_USER:
                    		setUser((User)value);break;
                    case SLOT_VIDEO:
                    		setVideo((Video)value);break;
                    case SLOT_WATCH_DURATION:
                    		if (value == null) throw new IllegalArgumentException("'watchDuration' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setWatchDuration((Integer)value);
                            break;
                    case SLOT_VIDEO_DURATION:
                    		if (value == null) throw new IllegalArgumentException("'videoDuration' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setVideoDuration((Integer)value);
                            break;
                    case SLOT_WATCH_COMPLETION_RATE:
                    		break;
                    default: throw new IllegalArgumentException("Illegal property id for \"com.celeste.entity.VideoView\": \"" + prop + "\"");
                }
            }

            @SuppressWarnings("unchecked")
            @Override
            public void __set(String prop, Object value) {
                switch (prop) {
                    case "createTime":
                    		setCreateTime((Date)value);break;
                    case "modifyTime":
                    		setModifyTime((Date)value);break;
                    case "betweenTime":
                    		break;
                    case "id":
                    		if (value == null) throw new IllegalArgumentException("'id' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setId((Long)value);
                            break;
                    case "user":
                    		setUser((User)value);break;
                    case "video":
                    		setVideo((Video)value);break;
                    case "watchDuration":
                    		if (value == null) throw new IllegalArgumentException("'watchDuration' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setWatchDuration((Integer)value);
                            break;
                    case "videoDuration":
                    		if (value == null) throw new IllegalArgumentException("'videoDuration' cannot be null, if you want to set null, please use any annotation whose simple name is \"Nullable\" to decorate the property");
                            setVideoDuration((Integer)value);
                            break;
                    case "watchCompletionRate":
                    		break;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.VideoView\": \"" + prop + "\"");
                }
            }

            @Override
            public void __show(PropId prop, boolean visible) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Visibility __visibility = (__modified!= null ? __modified : __base).__visibility;
                if (__visibility == null) {
                    if (visible) {
                        return;
                    }
                    __modified().__visibility = __visibility = Visibility.of(9);
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__show(prop.asName(), visible);
                    return;
                    case SLOT_CREATE_TIME:
                    		__visibility.show(SLOT_CREATE_TIME, visible);break;
                    case SLOT_MODIFY_TIME:
                    		__visibility.show(SLOT_MODIFY_TIME, visible);break;
                    case SLOT_BETWEEN_TIME:
                    		__visibility.show(SLOT_BETWEEN_TIME, visible);break;
                    case SLOT_ID:
                    		__visibility.show(SLOT_ID, visible);break;
                    case SLOT_USER:
                    		__visibility.show(SLOT_USER, visible);break;
                    case SLOT_VIDEO:
                    		__visibility.show(SLOT_VIDEO, visible);break;
                    case SLOT_WATCH_DURATION:
                    		__visibility.show(SLOT_WATCH_DURATION, visible);break;
                    case SLOT_VIDEO_DURATION:
                    		__visibility.show(SLOT_VIDEO_DURATION, visible);break;
                    case SLOT_WATCH_COMPLETION_RATE:
                    		__visibility.show(SLOT_WATCH_COMPLETION_RATE, visible);break;
                    default: throw new IllegalArgumentException(
                                "Illegal property id for \"com.celeste.entity.VideoView\": \"" + 
                                prop + 
                                "\",it does not exists"
                            );
                }
            }

            @Override
            public void __show(String prop, boolean visible) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                Visibility __visibility = (__modified!= null ? __modified : __base).__visibility;
                if (__visibility == null) {
                    if (visible) {
                        return;
                    }
                    __modified().__visibility = __visibility = Visibility.of(9);
                }
                switch (prop) {
                    case "createTime":
                    		__visibility.show(SLOT_CREATE_TIME, visible);break;
                    case "modifyTime":
                    		__visibility.show(SLOT_MODIFY_TIME, visible);break;
                    case "betweenTime":
                    		__visibility.show(SLOT_BETWEEN_TIME, visible);break;
                    case "id":
                    		__visibility.show(SLOT_ID, visible);break;
                    case "user":
                    		__visibility.show(SLOT_USER, visible);break;
                    case "video":
                    		__visibility.show(SLOT_VIDEO, visible);break;
                    case "watchDuration":
                    		__visibility.show(SLOT_WATCH_DURATION, visible);break;
                    case "videoDuration":
                    		__visibility.show(SLOT_VIDEO_DURATION, visible);break;
                    case "watchCompletionRate":
                    		__visibility.show(SLOT_WATCH_COMPLETION_RATE, visible);break;
                    default: throw new IllegalArgumentException(
                                "Illegal property name for \"com.celeste.entity.VideoView\": \"" + 
                                prop + 
                                "\",it does not exists"
                            );
                }
            }

            @Override
            public void __unload(PropId prop) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                int __propIndex = prop.asIndex();
                switch (__propIndex) {
                    case -1:
                    		__unload(prop.asName());
                    return;
                    case SLOT_CREATE_TIME:
                    		__modified().__createTimeValue = null;break;
                    case SLOT_MODIFY_TIME:
                    		__modified().__modifyTimeLoaded = false;break;
                    case SLOT_BETWEEN_TIME:
                    		break;
                    case SLOT_ID:
                    		__modified().__idLoaded = false;break;
                    case SLOT_USER:
                    		__modified().__userLoaded = false;break;
                    case SLOT_VIDEO:
                    		__modified().__videoLoaded = false;break;
                    case SLOT_WATCH_DURATION:
                    		__modified().__watchDurationLoaded = false;break;
                    case SLOT_VIDEO_DURATION:
                    		__modified().__videoDurationLoaded = false;break;
                    case SLOT_WATCH_COMPLETION_RATE:
                    		break;
                    default: throw new IllegalArgumentException("Illegal property id for \"com.celeste.entity.VideoView\": \"" + prop + "\", it does not exist or its loaded state is not controllable");
                }
            }

            @Override
            public void __unload(String prop) {
                if (__resolved != null) {
                    throw new IllegalStateException("The current draft has been resolved so it cannot be modified");
                }
                switch (prop) {
                    case "createTime":
                    		__modified().__createTimeValue = null;break;
                    case "modifyTime":
                    		__modified().__modifyTimeLoaded = false;break;
                    case "betweenTime":
                    		break;
                    case "id":
                    		__modified().__idLoaded = false;break;
                    case "user":
                    		__modified().__userLoaded = false;break;
                    case "video":
                    		__modified().__videoLoaded = false;break;
                    case "watchDuration":
                    		__modified().__watchDurationLoaded = false;break;
                    case "videoDuration":
                    		__modified().__videoDurationLoaded = false;break;
                    case "watchCompletionRate":
                    		break;
                    default: throw new IllegalArgumentException("Illegal property name for \"com.celeste.entity.VideoView\": \"" + prop + "\", it does not exist or its loaded state is not controllable");
                }
            }

            @Override
            public DraftContext __draftContext() {
                return __ctx;
            }

            @Override
            public Object __resolve() {
                if (__resolved != null) {
                    return __resolved;
                }
                if (__resolving) {
                    throw new CircularReferenceException();
                }
                __resolving = true;
                try {
                    Implementor base = __base;
                    Impl __tmpModified = __modified;
                    if (__tmpModified == null) {
                        if (base.__isLoaded(PropId.byIndex(SLOT_USER))) {
                            User oldValue = base.user();
                            User newValue = __ctx.resolveObject(oldValue);
                            if (oldValue != newValue) {
                                setUser(newValue);
                            }
                        }
                        if (base.__isLoaded(PropId.byIndex(SLOT_VIDEO))) {
                            Video oldValue = base.video();
                            Video newValue = __ctx.resolveObject(oldValue);
                            if (oldValue != newValue) {
                                setVideo(newValue);
                            }
                        }
                        __tmpModified = __modified;
                    }
                    else {
                        __tmpModified.__userValue = __ctx.resolveObject(__tmpModified.__userValue);
                        __tmpModified.__videoValue = __ctx.resolveObject(__tmpModified.__videoValue);
                    }
                    if (__base != null && __tmpModified == null) {
                        this.__resolved = base;
                        return base;
                    }
                    this.__resolved = __tmpModified;
                    return __tmpModified;
                }
                finally {
                    __resolving = false;
                }
            }

            @Override
            public boolean __isResolved() {
                return __resolved != null;
            }

            Impl __modified() {
                Impl __tmpModified = __modified;
                if (__tmpModified == null) {
                    __tmpModified = __base.clone();
                    __modified = __tmpModified;
                }
                return __tmpModified;
            }
        }
    }

    @GeneratedBy(
            type = VideoView.class
    )
    class Builder {
        private final Producer.DraftImpl __draft;

        public Builder() {
            __draft = new Producer.DraftImpl(null, null);
            __draft.__show(PropId.byIndex(Producer.SLOT_CREATE_TIME), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_BETWEEN_TIME), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_WATCH_DURATION), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_VIDEO_DURATION), false);
            __draft.__show(PropId.byIndex(Producer.SLOT_WATCH_COMPLETION_RATE), false);
        }

        public Builder id(Long id) {
            if (id != null) {
                __draft.setId(id);
            }
            return this;
        }

        public Builder createTime(Date createTime) {
            if (createTime != null) {
                __draft.setCreateTime(createTime);
                __draft.__show(PropId.byIndex(Producer.SLOT_CREATE_TIME), true);
            }
            return this;
        }

        @Nullable
        public Builder modifyTime(Date modifyTime) {
            __draft.setModifyTime(modifyTime);
            return this;
        }

        @Nullable
        public Builder user(User user) {
            __draft.setUser(user);
            return this;
        }

        @Nullable
        public Builder video(Video video) {
            __draft.setVideo(video);
            return this;
        }

        public Builder watchDuration(Integer watchDuration) {
            if (watchDuration != null) {
                __draft.setWatchDuration(watchDuration);
                __draft.__show(PropId.byIndex(Producer.SLOT_WATCH_DURATION), true);
            }
            return this;
        }

        public Builder videoDuration(Integer videoDuration) {
            if (videoDuration != null) {
                __draft.setVideoDuration(videoDuration);
                __draft.__show(PropId.byIndex(Producer.SLOT_VIDEO_DURATION), true);
            }
            return this;
        }

        public VideoView build() {
            return (VideoView)__draft.__modified();
        }
    }
}
