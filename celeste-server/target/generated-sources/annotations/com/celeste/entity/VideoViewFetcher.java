package com.celeste.entity;

import java.lang.Override;
import java.util.function.Consumer;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.lang.NewChain;
import org.babyfish.jimmer.meta.ImmutableProp;
import org.babyfish.jimmer.sql.ast.table.Table;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.babyfish.jimmer.sql.fetcher.FieldConfig;
import org.babyfish.jimmer.sql.fetcher.IdOnlyFetchType;
import org.babyfish.jimmer.sql.fetcher.impl.FetcherImpl;
import org.babyfish.jimmer.sql.fetcher.spi.AbstractTypedFetcher;

@GeneratedBy(
        type = VideoView.class
)
public class VideoViewFetcher extends AbstractTypedFetcher<VideoView, VideoViewFetcher> {
    public static final VideoViewFetcher $ = new VideoViewFetcher(null);

    private VideoViewFetcher(FetcherImpl<VideoView> base) {
        super(VideoView.class, base);
    }

    private VideoViewFetcher(VideoViewFetcher prev, ImmutableProp prop, boolean negative,
            IdOnlyFetchType idOnlyFetchType) {
        super(prev, prop, negative, idOnlyFetchType);
    }

    private VideoViewFetcher(VideoViewFetcher prev, ImmutableProp prop,
            FieldConfig<?, ? extends Table<?>> fieldConfig) {
        super(prev, prop, fieldConfig);
    }

    public static VideoViewFetcher $from(Fetcher<VideoView> base) {
        return base instanceof VideoViewFetcher ? 
        	(VideoViewFetcher)base : 
        	new VideoViewFetcher((FetcherImpl<VideoView>)base);
    }

    @NewChain
    public VideoViewFetcher createTime() {
        return add("createTime");
    }

    @NewChain
    public VideoViewFetcher createTime(boolean enabled) {
        return enabled ? add("createTime") : remove("createTime");
    }

    @NewChain
    public VideoViewFetcher modifyTime() {
        return add("modifyTime");
    }

    @NewChain
    public VideoViewFetcher modifyTime(boolean enabled) {
        return enabled ? add("modifyTime") : remove("modifyTime");
    }

    @NewChain
    public VideoViewFetcher betweenTime() {
        return add("betweenTime");
    }

    @NewChain
    public VideoViewFetcher betweenTime(boolean enabled) {
        return enabled ? add("betweenTime") : remove("betweenTime");
    }

    @NewChain
    public VideoViewFetcher user() {
        return add("user");
    }

    @NewChain
    public VideoViewFetcher user(boolean enabled) {
        return enabled ? add("user") : remove("user");
    }

    @NewChain
    public VideoViewFetcher user(Fetcher<User> childFetcher) {
        return add("user", childFetcher);
    }

    @NewChain
    public VideoViewFetcher user(Fetcher<User> childFetcher,
            Consumer<FieldConfig<User, UserTable>> fieldConfig) {
        return add("user", childFetcher, fieldConfig);
    }

    @NewChain
    public VideoViewFetcher user(IdOnlyFetchType idOnlyFetchType) {
        return add("user", idOnlyFetchType);
    }

    @NewChain
    public VideoViewFetcher video() {
        return add("video");
    }

    @NewChain
    public VideoViewFetcher video(boolean enabled) {
        return enabled ? add("video") : remove("video");
    }

    @NewChain
    public VideoViewFetcher video(Fetcher<Video> childFetcher) {
        return add("video", childFetcher);
    }

    @NewChain
    public VideoViewFetcher video(Fetcher<Video> childFetcher,
            Consumer<FieldConfig<Video, VideoTable>> fieldConfig) {
        return add("video", childFetcher, fieldConfig);
    }

    @NewChain
    public VideoViewFetcher video(IdOnlyFetchType idOnlyFetchType) {
        return add("video", idOnlyFetchType);
    }

    @NewChain
    public VideoViewFetcher watchDuration() {
        return add("watchDuration");
    }

    @NewChain
    public VideoViewFetcher watchDuration(boolean enabled) {
        return enabled ? add("watchDuration") : remove("watchDuration");
    }

    @NewChain
    public VideoViewFetcher videoDuration() {
        return add("videoDuration");
    }

    @NewChain
    public VideoViewFetcher videoDuration(boolean enabled) {
        return enabled ? add("videoDuration") : remove("videoDuration");
    }

    @NewChain
    public VideoViewFetcher watchCompletionRate() {
        return add("watchCompletionRate");
    }

    @NewChain
    public VideoViewFetcher watchCompletionRate(boolean enabled) {
        return enabled ? add("watchCompletionRate") : remove("watchCompletionRate");
    }

    @Override
    protected VideoViewFetcher createFetcher(ImmutableProp prop, boolean negative,
            IdOnlyFetchType idOnlyFetchType) {
        return new VideoViewFetcher(this, prop, negative, idOnlyFetchType);
    }

    @Override
    protected VideoViewFetcher createFetcher(ImmutableProp prop,
            FieldConfig<?, ? extends Table<?>> fieldConfig) {
        return new VideoViewFetcher(this, prop, fieldConfig);
    }
}
