package com.celeste.entity;

import java.lang.Double;
import java.lang.Integer;
import java.lang.Long;
import java.lang.String;
import java.util.Date;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.meta.ImmutableType;
import org.babyfish.jimmer.meta.TypedProp;
import org.babyfish.jimmer.sql.JoinType;
import org.babyfish.jimmer.sql.ast.PropExpression;
import org.babyfish.jimmer.sql.ast.table.PropsFor;

@GeneratedBy(
        type = VideoView.class
)
@PropsFor(VideoView.class)
public interface VideoViewProps extends BaseProps {
    TypedProp.Scalar<VideoView, Long> ID = 
        TypedProp.scalar(ImmutableType.get(VideoView.class).getProp("id"));

    TypedProp.Scalar<VideoView, Date> CREATE_TIME = 
        TypedProp.scalar(ImmutableType.get(VideoView.class).getProp("createTime"));

    TypedProp.Scalar<VideoView, Date> MODIFY_TIME = 
        TypedProp.scalar(ImmutableType.get(VideoView.class).getProp("modifyTime"));

    TypedProp.Scalar<VideoView, String> BETWEEN_TIME = 
        TypedProp.scalar(ImmutableType.get(VideoView.class).getProp("betweenTime"));

    TypedProp.Reference<VideoView, User> USER = 
        TypedProp.reference(ImmutableType.get(VideoView.class).getProp("user"));

    TypedProp.Reference<VideoView, Video> VIDEO = 
        TypedProp.reference(ImmutableType.get(VideoView.class).getProp("video"));

    TypedProp.Scalar<VideoView, Integer> WATCH_DURATION = 
        TypedProp.scalar(ImmutableType.get(VideoView.class).getProp("watchDuration"));

    TypedProp.Scalar<VideoView, Integer> VIDEO_DURATION = 
        TypedProp.scalar(ImmutableType.get(VideoView.class).getProp("videoDuration"));

    TypedProp.Scalar<VideoView, Double> WATCH_COMPLETION_RATE = 
        TypedProp.scalar(ImmutableType.get(VideoView.class).getProp("watchCompletionRate"));

    PropExpression.Num<Long> id();

    UserTable user();

    UserTable user(JoinType joinType);

    PropExpression.Num<Long> userId();

    VideoTable video();

    VideoTable video(JoinType joinType);

    PropExpression.Num<Long> videoId();

    PropExpression.Num<Integer> watchDuration();

    PropExpression.Num<Integer> videoDuration();

    PropExpression.Num<Double> watchCompletionRate();
}
