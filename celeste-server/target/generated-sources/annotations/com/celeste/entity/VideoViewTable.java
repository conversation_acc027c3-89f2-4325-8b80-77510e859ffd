package com.celeste.entity;

import java.lang.Deprecated;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Override;
import java.lang.String;
import java.util.Date;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.sql.JoinType;
import org.babyfish.jimmer.sql.ast.PropExpression;
import org.babyfish.jimmer.sql.ast.impl.table.TableImplementor;
import org.babyfish.jimmer.sql.ast.table.TableEx;
import org.babyfish.jimmer.sql.ast.table.spi.AbstractTypedTable;

@GeneratedBy(
        type = VideoView.class
)
public class VideoViewTable extends AbstractTypedTable<VideoView> implements VideoViewProps {
    public static final VideoViewTable $ = new VideoViewTable();

    public VideoViewTable() {
        super(VideoView.class);
    }

    public VideoViewTable(AbstractTypedTable.DelayedOperation<VideoView> delayedOperation) {
        super(VideoView.class, delayedOperation);
    }

    public VideoViewTable(TableImplementor<VideoView> table) {
        super(table);
    }

    protected VideoViewTable(VideoViewTable base, String joinDisabledReason) {
        super(base, joinDisabledReason);
    }

    @Override
    public PropExpression.Num<Long> id() {
        return __get(VideoViewProps.ID.unwrap());
    }

    @Override
    public PropExpression.Cmp<Date> createTime() {
        return __get(VideoViewProps.CREATE_TIME.unwrap());
    }

    @Override
    public PropExpression.Cmp<Date> modifyTime() {
        return __get(VideoViewProps.MODIFY_TIME.unwrap());
    }

    @Override
    public UserTable user() {
        __beforeJoin();
        if (raw != null) {
            return new UserTable(raw.joinImplementor(VideoViewProps.USER.unwrap()));
        }
        return new UserTable(joinOperation(VideoViewProps.USER.unwrap()));
    }

    @Override
    public UserTable user(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new UserTable(raw.joinImplementor(VideoViewProps.USER.unwrap(), joinType));
        }
        return new UserTable(joinOperation(VideoViewProps.USER.unwrap(), joinType));
    }

    @Override
    public PropExpression.Num<Long> userId() {
        return __getAssociatedId(VideoViewProps.USER.unwrap());
    }

    @Override
    public VideoTable video() {
        __beforeJoin();
        if (raw != null) {
            return new VideoTable(raw.joinImplementor(VideoViewProps.VIDEO.unwrap()));
        }
        return new VideoTable(joinOperation(VideoViewProps.VIDEO.unwrap()));
    }

    @Override
    public VideoTable video(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new VideoTable(raw.joinImplementor(VideoViewProps.VIDEO.unwrap(), joinType));
        }
        return new VideoTable(joinOperation(VideoViewProps.VIDEO.unwrap(), joinType));
    }

    @Override
    public PropExpression.Num<Long> videoId() {
        return __getAssociatedId(VideoViewProps.VIDEO.unwrap());
    }

    @Override
    public PropExpression.Num<Integer> watchDuration() {
        return __get(VideoViewProps.WATCH_DURATION.unwrap());
    }

    @Override
    public PropExpression.Num<Integer> videoDuration() {
        return __get(VideoViewProps.VIDEO_DURATION.unwrap());
    }

    @Override
    public VideoViewTableEx asTableEx() {
        return new VideoViewTableEx(this, null);
    }

    @Override
    public VideoViewTable __disableJoin(String reason) {
        return new VideoViewTable(this, reason);
    }

    @GeneratedBy(
            type = VideoView.class
    )
    public static class Remote extends AbstractTypedTable<VideoView> {
        public Remote(AbstractTypedTable.DelayedOperation delayedOperation) {
            super(VideoView.class, delayedOperation);
        }

        public Remote(TableImplementor<VideoView> table) {
            super(table);
        }

        public PropExpression.Num<Long> id() {
            return __get(VideoViewProps.ID.unwrap());
        }

        @Override
        @Deprecated
        public TableEx<VideoView> asTableEx() {
            throw new UnsupportedOperationException();
        }

        @Override
        public Remote __disableJoin(String reason) {
            return this;
        }
    }
}
