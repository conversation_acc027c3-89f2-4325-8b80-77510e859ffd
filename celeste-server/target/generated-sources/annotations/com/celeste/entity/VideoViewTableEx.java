package com.celeste.entity;

import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.sql.JoinType;
import org.babyfish.jimmer.sql.ast.impl.table.TableImplementor;
import org.babyfish.jimmer.sql.ast.impl.table.TableProxies;
import org.babyfish.jimmer.sql.ast.table.Table;
import org.babyfish.jimmer.sql.ast.table.TableEx;
import org.babyfish.jimmer.sql.ast.table.WeakJoin;
import org.babyfish.jimmer.sql.ast.table.spi.AbstractTypedTable;

@GeneratedBy(
        type = VideoView.class
)
public class VideoViewTableEx extends VideoViewTable implements TableEx<VideoView> {
    public static final VideoViewTableEx $ = new VideoViewTableEx(VideoViewTable.$, null);

    public VideoViewTableEx() {
        super();
    }

    public VideoViewTableEx(AbstractTypedTable.DelayedOperation<VideoView> delayedOperation) {
        super(delayedOperation);
    }

    public VideoViewTableEx(TableImplementor<VideoView> table) {
        super(table);
    }

    protected VideoViewTableEx(VideoViewTable base, String joinDisabledReason) {
        super(base, joinDisabledReason);
    }

    public UserTableEx user() {
        __beforeJoin();
        if (raw != null) {
            return new UserTableEx(raw.joinImplementor(VideoViewProps.USER.unwrap()));
        }
        return new UserTableEx(joinOperation(VideoViewProps.USER.unwrap()));
    }

    public UserTableEx user(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new UserTableEx(raw.joinImplementor(VideoViewProps.USER.unwrap(), joinType));
        }
        return new UserTableEx(joinOperation(VideoViewProps.USER.unwrap(), joinType));
    }

    public VideoTableEx video() {
        __beforeJoin();
        if (raw != null) {
            return new VideoTableEx(raw.joinImplementor(VideoViewProps.VIDEO.unwrap()));
        }
        return new VideoTableEx(joinOperation(VideoViewProps.VIDEO.unwrap()));
    }

    public VideoTableEx video(JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return new VideoTableEx(raw.joinImplementor(VideoViewProps.VIDEO.unwrap(), joinType));
        }
        return new VideoTableEx(joinOperation(VideoViewProps.VIDEO.unwrap(), joinType));
    }

    @Override
    public VideoViewTableEx asTableEx() {
        return this;
    }

    @Override
    public VideoViewTableEx __disableJoin(String reason) {
        return new VideoViewTableEx(this, reason);
    }

    public <TT extends Table<?>, WJ extends WeakJoin<VideoViewTable, TT>> TT weakJoin(
            Class<WJ> weakJoinType) {
        return weakJoin(weakJoinType, JoinType.INNER);
    }

    @SuppressWarnings("unchecked")
    public <TT extends Table<?>, WJ extends WeakJoin<VideoViewTable, TT>> TT weakJoin(
            Class<WJ> weakJoinType, JoinType joinType) {
        __beforeJoin();
        if (raw != null) {
            return (TT)TableProxies.wrap(raw.weakJoinImplementor(weakJoinType, joinType));
        }
        return (TT)TableProxies.fluent(joinOperation(weakJoinType, joinType));
    }
}
